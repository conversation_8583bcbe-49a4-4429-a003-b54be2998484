<?php

namespace app\admin\model;

use app\admin\traits\ModelTrait;
use app\common\model\TimeModel;

class ZcAssetOrderLog extends TimeModel
{
	
	use ModelTrait;
	
	protected $name = "zc_asset_order_log";
	
	protected $deleteTime = "delete_time";
	
	public function setEndDateAttr($value)
	{
		return $value ? strtotime($value) : '';
	}
	
	public function getEndDateAttr($value)
	{
		return $value ? date("Y-m-d", $value) : '';
	}
	
	
	public function logs()
    {
        return $this->belongsTo('\app\admin\model\ZcAssetOrder', 'order_id', 'id');
    }
	
	public function asset()
	{
		return $this->belongsTo('\app\admin\model\ZcAsset', 'asset_id', 'id');
	}
	
	
}