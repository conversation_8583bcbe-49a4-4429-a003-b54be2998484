<?php

namespace app\admin\model;

use app\admin\traits\ModelTrait;
use app\common\model\TimeModel;
use think\facade\Log;
use zjkal\TimeHelper;

class ZcAssetOrder extends TimeModel
{
	use ModelTrait;
	
	protected $name = "zc_asset_order";
	
	protected $deleteTime = "delete_time";
	
	public function setStartDateAttr($value)
	{
		return $value
			? strtotime($value)
			: '';
	}
	
	public function getStartDateAttr($value)
	{
		return $value
			? date("Y-m-d", $value)
			: '';
	}
	
	public function setEndDateAttr($value)
	{
		$ide = strtotime($value);
		return $value
			? strtotime($value)
			: null;
	}
	
	public function getEndDateAttr($value)
	{
		return $value
			? date("Y-m-d", $value)
			: '';
	}
	
	public function getPayTypeTextAttr($value)
	{
		return $this->getPayTypeList()[$value] ?? '';
	}
	
	
	public function asset()
	{
		return $this->belongsTo('\app\admin\model\ZcAsset', 'asset_id', 'id');
	}
	
	public function zulin()
	{
		return $this->belongsTo('\app\admin\model\ZcZulin', 'zulin_id', 'id');
	}
	
	public function qiandings()
	{
		return $this->belongsTo('\app\admin\model\SystemDept', 'qianding', 'id');
	}
	
	public function dept()
	{
		return $this->belongsTo('\app\admin\model\SystemDept', 'dept_suoshu', 'id');
	}
	
	public function logs()
	{
		return $this->hasMany('\app\admin\model\ZcAssetOrderLog', 'order_id', 'id');
	}
	
	
	public function getDeptSuoshuList($type = 0)
	{
		if ($type) {
			$arr = [];
		}
		else {
			$arr = [
				[
					'id'   => 0,
					'name' => '咸运集团',
				],
			];
		}
		$dept_id = session('admin.dept_id');
		$where   = [];
		if ($dept_id > 0) {
			$where[] = [
				'id',
				'=',
				$dept_id
			];
		}
		$list = SystemDept::where($where)
		                  ->field('id,name')
		                  ->select();
		return array_merge($arr, $list->toArray());
	}
	
	public function getQiandingList()
	{
		return [
			'0' => '咸运集团',
			'1' => '直属单位',
		];
	}
	
	public function getPayTypeList()
	{
		return [
			'0' => '月付',
			'1' => '季付',
			'2' => '半年付',
			'3' => '年付',
			'4' => '一次性结清',
		];
	}
	
	public function getAssetList()
	{
		$where     = [];
		$dataScope = $this->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$list = ZcAsset::where($where)
		               ->field('title,id')
		               ->where('status', 0)
		               ->select();
		return $list;
	}
	
	public function getZulinList()
	{
		$where     = [];
		$dataScope = $this->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$list = ZcZulin::where($where)
		               ->field('title,id')
		               ->select();
		return $list;
	}
	
	
	/*public static function calcQianfei_backup($orderInfo)
	{
		$bill      = bcdiv($orderInfo['bill'], 100);
		$day_price = bcmul($bill, $orderInfo['price'], 2);
		$endDate   = date('Y-m-d');
		$orderLog  = ZcAssetOrderLog::where('order_id', $orderInfo['id'])
		                            ->order('create_time desc')
		                            ->findOrEmpty();
		if ($orderLog->isEmpty()) {
			$startDate = $orderInfo['start_date'];
		}
		else {
			$startDate = $orderLog['end_date'];
		}
		$days     = TimeHelper::diffDays($endDate, $startDate);
		$zhinajin = bcmul($days, $day_price, 2);
		$month    = TimeHelper::diffMonths($endDate, $startDate);
		$qianfei  = bcmul($orderInfo['price'], $month, 2);
		$leiji    = bcadd($qianfei, $zhinajin, 2);
		$minzu    = empty($orderInfo['mianzu'])
			? 0
			: $orderInfo['mianzu'];
		$leiji    = bcsub($leiji, $minzu, 2);
		$orderInfo->save([
			'qianfei'  => $qianfei,
			'zhinajin' => $zhinajin,
			'leiji'    => $leiji,
		]);
		return [
			'qianfei'  => $qianfei,
			'zhinajin' => $zhinajin,
			'leiji'    => $leiji,
		];
	}*/
	
	public static function calcQianfei($orderInfo)
	{
		// 记录初始信息
		Log::info('开始计算欠费，订单ID: ' . $orderInfo['id'] . '，原始价格: ' . $orderInfo['price'] . '，递增百分比: ' . $orderInfo['dizeng'] . '，递增日期: ' . $orderInfo['dizeng_date']);
		
		// 处理按年递增
		$originalPrice = $orderInfo['price'];
		if (!empty($orderInfo['dizeng']) && !empty($orderInfo['dizeng_date'])) {
			$currentDate  = strtotime(date('Y-m-d'));
			$increaseDate = strtotime($orderInfo['dizeng_date']);
			$increaseRate = floatval($orderInfo['dizeng']) / 100;
			$newPrice     = $originalPrice;
			
			Log::info('递增计算 - 当前日期: ' . date('Y-m-d', $currentDate) . '，递增日期: ' . date('Y-m-d', $increaseDate) . '，递增比例: ' . ($increaseRate * 100) . '%');
			
			// 循环处理可能的多次递增
			$priceChanged  = false;
			$increaseCount = 0;
			while ($currentDate >= $increaseDate) {
				$increaseCount++;
				$oldPrice = $newPrice;
				// 执行递增
				$newPrice = bcmul($newPrice, (1 + $increaseRate), 2);
				
				Log::info('第' . $increaseCount . '次递增 - 原价: ' . $oldPrice . '，递增后价格: ' . $newPrice . '，递增日期: ' . date('Y-m-d', $increaseDate));
				
				// 更新下一个递增日期（加一年）
				$increaseDate = strtotime('+1 year', $increaseDate);
				$priceChanged = true;
			}
			
			// 如果价格有变化，更新合同价格和下一个递增日期
			if ($priceChanged) {
				Log::info('价格已变更 - 原始价格: ' . $originalPrice . '，新价格: ' . $newPrice . '，下次递增日期: ' . date('Y-m-d', $increaseDate));
				
				$orderInfo['price']       = $newPrice;
				$orderInfo['dizeng_date'] = date('Y-m-d', $increaseDate);
				// 保存更新后的价格和递增日期
				$orderInfo->save([
					'price'       => $newPrice,
					'dizeng_date' => date('Y-m-d', $increaseDate)
				]);
			}
			else {
				Log::info('价格未变更，当前日期未达到递增日期');
			}
		}
		else {
			Log::info('未设置递增参数，跳过递增计算');
		}
		
		// 继续原有的欠费计算逻辑
		$bill      = bcdiv($orderInfo['bill'], 100);
		$price     = bcdiv($orderInfo['price'], 30, 2);
		$day_price = bcmul($bill, $price, 2); // 这里使用可能已更新的价格
		$endDate   = date('Y-m-d');
		$orderLog  = ZcAssetOrderLog::where('order_id', $orderInfo['id'])
		                            ->order('create_time desc')
		                            ->findOrEmpty();
		if ($orderLog->isEmpty()) {
			$startDate = $orderInfo['start_date'];
			Log::info('未找到付款记录，使用合同起始日期: ' . $startDate);
		}
		else {
			$startDate = $orderLog['end_date'];
			Log::info('使用最近付款记录的结束日期: ' . $startDate);
		}
		$days     = TimeHelper::diffDays($endDate, $startDate);
		$zhinajin = bcmul($days, $day_price, 2);
		$month    = TimeHelper::diffMonths($endDate, $startDate);
		$qianfei  = bcmul($orderInfo['price'], $month, 2); // 使用可能已更新的价格
		$leiji    = bcadd($qianfei, $zhinajin, 2);
		$minzu    = empty($orderInfo['mianzu'])
			? 0
			: $orderInfo['mianzu'];
		$leiji    = bcsub($leiji, $minzu, 2);
		
		Log::info('欠费计算完成 - 计算周期: ' . $startDate . ' 至 ' . $endDate . '，天数: ' . $days . '，月数: ' . $month);
		Log::info('欠费金额: ' . $qianfei . '，滞纳金: ' . $zhinajin . '，免租金额: ' . $minzu . '，累计欠费: ' . $leiji);
		
		$orderInfo->save([
			'qianfei'  => $qianfei,
			'zhinajin' => $zhinajin,
			'leiji'    => $leiji,
		]);
		return [
			'qianfei'  => $qianfei,
			'zhinajin' => $zhinajin,
			'leiji'    => $leiji,
		];
	}
	
	// 应收，实收，到期数，费用总数，到期欠费总金额
	public static function calcOrder($type = 0, $year = '')
	{
		$zcAssetOrderObj = new self();
		if ($type == 0) {
			$zcAssetOrderObj = $zcAssetOrderObj->whereDay('end_date');
		}
		elseif ($type == 1) {
			$zcAssetOrderObj = $zcAssetOrderObj->whereMonth('end_date');
		}
		elseif ($type == 2) {
			$zcAssetOrderObj = $zcAssetOrderObj->whereYear('end_date');
			$year = date('Y'); // type=2时使用当前年份
		}
		elseif ($type == 4) {
			$zcAssetOrderObj = $zcAssetOrderObj->whereYear('end_date', $year);
		}
		$count = $zcAssetOrderObj->count(); // 到期总数
		
		$list = $zcAssetOrderObj->column('id');
		
		// 计算应收金额
		$yingshou = 0;
		if ($type == 2 || $type == 4) {
			// 按年计算应收
			$targetYear = $year;
			$allOrders = self::select(); // 获取所有订单
			
			foreach ($allOrders as $order) {
				$startDate = $order['start_date'];
				$endDate = $order['end_date'];
				$price = $order['price']; // 月租金
				
				$startYear = date('Y', strtotime($startDate));
				$endYear = date('Y', strtotime($endDate));
				
				// 计算合同在目标年度的部分
				$yearStart = $targetYear . '-01-01';
				$yearEnd = $targetYear . '-12-31';
				
				$calcStart = max($startDate, $yearStart);
				$calcEnd = min($endDate, $yearEnd);
				
				// 只有在目标年度有重叠的合同才计算
				if (strtotime($calcStart) <= strtotime($calcEnd)) {
					$months = TimeHelper::diffMonths($calcEnd, $calcStart);
					
					// 根据边界情况调整月数
					if ($startYear < $targetYear) {
						// 开始时间在前一年，需要-1（避免重复计算去年12月）
						$months = $months - 1;
					} elseif ($endYear > $targetYear) {
						// 结束时间在后一年，需要+1（计算今年12月）
						$months = $months + 1;
					} elseif ($startYear == $targetYear && $endYear == $targetYear) {
						// 完全在目标年内，+1
						$months = $months + 1;
					}
					
					// 确保月数不为负
					$months = max(0, $months);
					
					// 累加应收金额
					$orderYingshou = bcmul($price, $months, 2);
					$yingshou = bcadd($yingshou, $orderYingshou, 2);
				}
			}
		} else {
			// 其他类型保持原有逻辑
			$daoqiPrice = $zcAssetOrderObj->sum('leiji');
			$shishou = ZcAssetOrderLog::whereIn('order_id', $list)->sum('price');
			$yingshou = bcadd($daoqiPrice, $shishou, 2);
		}
		
		// 欠费总金额和实收金额
		$daoqiPrice = $zcAssetOrderObj->sum('leiji');
		$shishou = ZcAssetOrderLog::whereIn('order_id', $list)->sum('price');
		
		return [
			'daoqi'    => $count,
			'qianfei'  => $daoqiPrice,
			'yingshou' => $yingshou,
			'shishou'  => $shishou,
		];
	}
	
	public static function calcOrderTotal()
	{
		$zcAssetOrderObj = new self();
		$list            = $zcAssetOrderObj->column('id');
		
		// 到期总数
		$count = $zcAssetOrderObj->count();
		
		// 欠费总金额
		$daoqiPrice = $zcAssetOrderObj->sum('leiji');
		
		// 实收总金额
		$shishou = ZcAssetOrderLog::whereIn('order_id', $list)
		                          ->sum('price');
		
		return [
			'daoqi'    => $count,
			'qianfei'  => $daoqiPrice,
			'yingshou' => bcadd($daoqiPrice, $shishou, 2),
			'shishou'  => $shishou,
		];
	}
	
	
}
