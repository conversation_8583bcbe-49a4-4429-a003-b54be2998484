# php think curd -t zc_cat -r system_dept --foreignKey=dept_id --primaryKey=id
drop table if exists `sq_zc_cat`;
CREATE TABLE `sq_zc_cat`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `title`       varchar(20)      NOT NULL COMMENT '名称',
    `status`      tinyint(1) unsigned       DEFAULT 1 COMMENT '状态 {radio} (0:禁用,1:启用)',
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `title` (`title`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='资产分类';


# php think curd -t zc_asset -r zc_cat --foreignKey=cat_id --primaryKey=id
drop table if exists `sq_zc_asset`;
CREATE TABLE `sq_zc_asset`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `cat_id`      int(10) unsigned NOT NULL DEFAULT 0,
    `title`       varchar(20)      NOT NULL COMMENT '名称',
    `addr`        text COMMENT '位置',
    `latitude`    varchar(50)      NOT NULL default '' comment '纬度',
    `longitude`   varchar(50)      NOT NULL default '' comment '经度',
    `area`        varchar(50) COMMENT '面积',
    `image`       text COMMENT '平面图',
    `video`       text COMMENT '视频',
    `status`      tinyint(1) unsigned       DEFAULT 0 COMMENT '状态 {radio} (0:闲置,1:出租)',
    `price`       decimal(10, 2)   NOT NULL default 0 comment '单价',
    `year_price`  decimal(10, 2)   NOT NULL default 0 comment '年价',
    `remark`      text COMMENT '介绍',
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `title` (`title`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='资产管理';

alter table `sq_zc_asset`
    add column `video` text after `image`;

# php think curd -t zc_asset_order -r zc_asset --foreignKey=asset_id --primaryKey=id
drop table if exists `sq_zc_asset_order`;
CREATE TABLE `sq_zc_asset_order`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `asset_id`    int(10) unsigned NOT NULL default 0,
    `dept_suoshu` tinyint(1) unsigned       DEFAULT 0 COMMENT '所属单位 {radio} (0:咸运集团,1:其他)',
    `qianding`    tinyint(1) unsigned       DEFAULT 0 COMMENT '合同签订方 {radio} (0:咸运集团,1:直属单位)',
    `zulin_id`    int(10) unsigned NOT NULL default 0 COMMENT '租用单位名称',
    `yongtu`      text COMMENT '用途',
    `addr`        text COMMENT '位置',
    `contract_no` varchar(50)      NOT NULL default '' COMMENT '合同编号',
    `ya_price`    decimal(10, 2)   NOT NULL default 0 COMMENT '押金',
    `area`        varchar(50) COMMENT '面积',
    `start_date`  int(10)                   default null COMMENT '起始时间{date}(date)',
    `end_date`    int(10)                   default null COMMENT '到期时间{date}(date)',
    `days`        int(10)          NOT NULL default 0 COMMENT '总天数',
    `price`       decimal(10, 2)   NOT NULL default 0 COMMENT '租赁单价',
    `total`       decimal(10, 2)   NOT NULL default 0 COMMENT '租赁总价',
    `pay_type`    tinyint(1)       NOT NULL default 0 COMMENT '付费方式 {radio} (0:月付,1:季付,2:半年付,3:年付,4:一次性结清)',
    `files`       text COMMENT '合同附件',
    `dizeng`      text comment '递增百分比',
    `dizeng_date` date                   DEFAULT NULL COMMENT '递增时间',
    `qianfei`     decimal(10, 2) unsigned   DEFAULT 0 COMMENT '租金欠费金额',
    `shangjiao`   decimal(10, 2) unsigned   DEFAULT 0 COMMENT '上缴金额',
    `ziyong`      decimal(10, 2) unsigned   DEFAULT 0 COMMENT '自用金额',
    `mianzu`      varchar(50)      NOT NULL default '' COMMENT '免租期',
    `liyou`       text COMMENT '免租理由',
    `jingbanren`  text COMMENT '经办人信息',
    `bill`        int(10) unsigned          DEFAULT 0 COMMENT '滞纳金比例',
    `day_price`   decimal(10, 2) unsigned   DEFAULT 0 COMMENT '滞纳金金额/日',
    `zhinajin`    decimal(10, 2) unsigned   DEFAULT 0 COMMENT '滞纳金总额',
    `leiji`       decimal(10, 2) unsigned   DEFAULT 0 COMMENT '累计欠费',
    `last_time`   int(10) unsigned          DEFAULT null COMMENT '最后结算日期',
    `is_check`    tinyint(1) unsigned       DEFAULT 0 COMMENT '是否审核 {radio} (0:否,1:是)',
    `remark`      text COMMENT '备注',
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='台账管理';

alter table sq_zc_asset_order
    add column `dizeng_date` date                   DEFAULT NULL after `dizeng`;

# php think curd -t zc_asset_order_log -r zc_asset_order --foreignKey=order_id --primaryKey=id
drop table if exists `sq_zc_asset_order_log`;
CREATE TABLE `sq_zc_asset_order_log`
(
    `id`            int(10) unsigned NOT NULL AUTO_INCREMENT,
    `asset_id`      int(10) unsigned NOT NULL default 0,
    `order_id`      int(10) unsigned NOT NULL default 0,
    `price`         decimal(10, 2)   NOT NULL DEFAULT 0 COMMENT '房租',
    `zhinajin`      decimal(10, 2)   NOT NULL DEFAULT 0 COMMENT '滞纳金',
    `invoice_image` text COMMENT '发票图片',
    `pay_image`     text COMMENT '付款凭证图片',
    `pay_no`        varchar(50)      not null default '' COMMENT '付款凭证号',
    `end_date`      int(11) unsigned NOT NULL comment '结租日期{date}(date)',
    `create_by`     int(10) unsigned NOT NULL default 0,
    `create_time`   int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time`   int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time`   int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='台账续费表';

# php think curd -t zc_asset_order_msg -r zc_asset_order --foreignKey=order_id --primaryKey=id
/*drop table if exists ` sq_zc_asset_order_msg `;
CREATE TABLE ` sq_zc_asset_order_msg `
(
    ` id `          int(10) unsigned NOT NULL AUTO_INCREMENT,
    ` asset_id `    int(10) unsigned NOT NULL default 0,
    ` order_id `    int(10) unsigned NOT NULL default 0,
    ` title `       varchar(20)      NOT NULL COMMENT '标题',
    ` content `     text COMMENT '内容',
    ` desc `        text COMMENT '处理内容',
    ` dept_id `     int(10) unsigned NOT NULL,
    ` create_by `   int(10) unsigned NOT NULL default 0,
    ` create_time ` int(11)                   DEFAULT NULL COMMENT '创建时间',
    ` update_time ` int(11)                   DEFAULT NULL COMMENT '更新时间',
    ` delete_time ` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (` id `),
    UNIQUE KEY ` title ` (` title `) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='台账维护';*/

# php think curd -t zc_asset_order_event -r zc_asset_order --foreignKey=order_id --primaryKey=id
/*drop table if exists `sq_zc_asset_order_event`;
CREATE TABLE `sq_zc_asset_order_event`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `asset_id`    int(10) unsigned NOT NULL default 0,
    `order_id`    int(10) unsigned NOT NULL default 0,
    `title`       varchar(20)      NOT NULL COMMENT '标题',
    `content`     text COMMENT '内容',
    `desc`        text COMMENT '处理内容',
    `dept_id`     int(10) unsigned NOT NULL,
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='台账事件';*/

# php think curd -t zc_zulin
drop table if exists `sq_zc_zulin`;
CREATE TABLE `sq_zc_zulin`
(
    `id`             int(10) unsigned NOT NULL AUTO_INCREMENT,
    `no`             varchar(60)      NOT NULL default '' COMMENT '编号',
    `title`          varchar(20)      NOT NULL COMMENT '名称',
    `name`           varchar(30)      NOT NULL default '' comment '负责人姓名',
    `mobile`         varchar(20)      NOT NULL default '' comment '负责人电话',
    `id_card_images` text comment '负责人身份证图片',
    `fa_name`        varchar(30)      NOT NULL default '' comment '法人姓名',
    `fa_mobile`      varchar(20)      NOT NULL default '' comment '法人电话',
    `faren_images`   text comment '法人身份证图片',
    `desc`           text comment '备注',
    `score`          int(10)          NOT NULL default -1 comment '评分',
    `create_by`      int(10) unsigned NOT NULL default 0,
    `create_time`    int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time`    int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time`    int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `title` (`title`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='租赁单位';

# sq_system_admin增加姓名字段
alter table sq_system_admin
    add column `name` varchar(30) not null default '' comment '姓名';

alter table sq_system_dept
    add column `keshi` varchar(30) not null default '';


# php think curd -t zc_zixun -r zc_asset --foreignKey=asset_id --primaryKey=id
drop table if exists `sq_zc_zixun`;
CREATE TABLE `sq_zc_zixun`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `asset_id`    int(10)          NOT NULL COMMENT '资产名称',
    `name`        varchar(30)      NOT NULL default '' comment '咨询人姓名',
    `mobile`      varchar(20)      NOT NULL default '' comment '咨询人电话',
    `content`     text comment '咨询内容',
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='咨询表单';

drop table if exists `sq_zc_task`;
CREATE TABLE `sq_zc_task`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = COMPACT;

# 0108
# php think curd -t zc_asset_order_event -r zc_asset --foreignKey=asset_id --primaryKey=id
drop table if exists `sq_zc_asset_order_event`;
CREATE TABLE `sq_zc_asset_order_event`
(
    `id`               int(10) unsigned NOT NULL AUTO_INCREMENT,
    `asset_id`         int(10) unsigned NOT NULL default 0,
    `order_id`         int(10) unsigned NOT NULL default 0,
    `price`            decimal(10, 2)   NOT NULL DEFAULT 0 COMMENT '金额',
    `image`            text COMMENT '减免凭证图片',
    `event_start_date` int(11) unsigned NOT NULL comment '免租开始日期{date}(date)',
    `event_end_date`   int(11) unsigned NOT NULL comment '免租结束日期{date}(date)',
    `type`    tinyint(1) unsigned       DEFAULT 0 COMMENT '类型 {radio} (0:金额,1:日期)',
    `total_days`       int(10) unsigned NOT NULL default 0,
    `create_by`        int(10) unsigned NOT NULL default 0,
    `create_time`      int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time`      int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time`      int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='特请处理';

alter table sq_zc_asset_order
    add column `c_total` decimal(10,2) not null default 0;
