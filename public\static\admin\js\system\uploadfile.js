define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'system.uploadfile/index',
        add_url: 'system.uploadfile/add',
        delete_url: 'system.uploadfile/delete',
    };

    return {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',],
                cols: [[
                    {type: "checkbox"},
                    {type: 'numbers', title: '序号'},
                    /*{
                        field: 'upload_type',
                        minWidth: 80,
                        title: '存储位置',
                        search: 'select',
                        selectList: {'local': '本地', 'alioss': '阿里云', 'qnoss': '七牛云', ',txcos': '腾讯云'}
                    },*/
                    {
                        field: 'url', minWidth: 80, search: false, title: '预览信息', templet: function (d) {
                            if (d.mime_type.indexOf('video')) {
                                return '<img style="max-width: 200px; max-height: 40px;" src="' + d.url + '" data-image="' + d.original_name + '">'
                            } else {
                                return '';
                            }
                        }
                    },
                    {field: 'url', minWidth: 120, search: false, title: '保存地址', templet: ea.table.url},
                    {field: 'original_name', search: false, minWidth: 80, title: '文件原名'},
                    /*{field: 'mime_type', search: false, minWidth: 80, title: 'mime类型'},*/
                   /* {field: 'file_ext', search: false, minWidth: 80, title: '文件后缀'},*/
                    {field: 'create_time', minWidth: 80, title: '创建时间', search: 'range'},
                    /*{width: 250, title: '操作', templet: ea.table.tool, operat: ['delete']}*/
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
    };
});