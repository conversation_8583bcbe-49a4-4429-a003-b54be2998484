<?php

namespace app\admin\controller\system;


use app\admin\model\SystemAdmin;
use app\admin\model\SystemDept;
use app\admin\service\TriggerService;
use app\common\constants\AdminConstant;
use app\common\controller\AdminController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use think\App;

/**
 * Class Admin
 *
 * @package app\admin\controller\system
 * @ControllerAnnotation(title="管理员管理")
 */
class Admin extends AdminController
{
	
	use \app\admin\traits\Curd;
	
	protected $sort = [
		'sort' => 'desc',
		'id'   => 'desc',
	];
	
	public function __construct(App $app)
	{
		parent::__construct($app);
		$this->model = new SystemAdmin();
		$this->assign('auth_list', $this->model->getAuthList());
	}
	
	/**
	 * @NodeAnotation(title="列表")
	 */
	public function index()
	{
		if ($this->request->isAjax()) {
			if (input('selectFields')) {
				return $this->selectList();
			}
			list($page, $limit, $where) = $this->buildTableParames();
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[]   = [
					'create_by',
					'in',
					$dataScope
				];
			}
			$adminId = session('admin.id');
			$count = $this->model->where('id', '<>', 1)
			                     ->where('id', '<>', $adminId)
			                     ->where($where)
			                     ->count();
			$list  = $this->model->with([
				'dept' => function ($query) {
					$query->field('id,name');
				},
			])
			                     ->withoutField('password')
			                     ->where('id', '<>', 1)
			                     ->where('id', '<>', $adminId)
			                     ->where($where)
			                     ->page($page, $limit)
			                     ->order($this->sort)
			                     ->select();
			$data  = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		$deptList = (new SystemDept())->getDeptList();
		$this->assign('deptList', json_encode($deptList));
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="添加")
	 */
	public function add()
	{
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			/*$authIds          = $this->request->post('auth_ids', []);
			$post['auth_ids'] = implode(',', array_keys($authIds));*/
			$rule = [];
			$this->validate($post, $rule);
			// 查询用户名是否存在
			$user = $this->model->where('username', $post['username'])
			                    ->findOrEmpty();
			if (!$user->isEmpty()) {
				$this->error('用户名已存在');
			}
			try {
				$save = $this->model->saveFromCreateId($post);
			}
			catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('adminid',session('admin.id'));
		$deptList = (new SystemDept())->getDeptList();
		$this->assign('deptList',$deptList);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="编辑")
	 */
	public function edit($id)
	{
		$row = $this->model->find($id);
		empty($row) && $this->error('数据不存在');
		if ($this->request->isAjax()) {
			$post             = $this->request->post();
//			$authIds          = $this->request->post('auth_ids', []);
//			$post['auth_ids'] = implode(',', array_keys($authIds));
			$rule             = [];
			$this->validate($post, $rule);
			if (isset($row['password'])) {
				unset($row['password']);
			}
			$user = $this->model->where('username', $post['username'])
			                    ->findOrEmpty();
			if (!$user->isEmpty() && $user->username != $post['username']) {
				$this->error('用户名已存在');
			}
			try {
				$save = $row->saveFromCreateId($post);
				TriggerService::updateMenu($id);
			}
			catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('row', $row);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="编辑")
	 */
	public function password($id)
	{
		$row = $this->model->find($id);
		empty($row) && $this->error('数据不存在');
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [
				'password|登录密码'       => 'require',
				'password_again|确认密码' => 'require',
			];
			$this->validate($post, $rule);
			if ($post['password'] != $post['password_again']) {
				$this->error('两次密码输入不一致');
			}
			try {
				$save = $row->save([
					'password' => password($post['password']),
				]);
			}
			catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$row->auth_ids = explode(',', $row->auth_ids);
		$this->assign('row', $row);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="删除")
	 */
	public function delete($id)
	{
		$row = $this->model->whereIn('id', $id)
		                   ->select();
		$row->isEmpty() && $this->error('数据不存在');
		$id == AdminConstant::SUPER_ADMIN_ID && $this->error('超级管理员不允许修改');
		if (is_array($id)) {
			if (in_array(AdminConstant::SUPER_ADMIN_ID, $id)) {
				$this->error('超级管理员不允许修改');
			}
		}
		try {
			$save = $row->delete();
		}
		catch (\Exception $e) {
			$this->error('删除失败');
		}
		$save
			? $this->success('删除成功')
			: $this->error('删除失败');
	}
	
	/**
	 * @NodeAnotation(title="属性修改")
	 */
	public function modify()
	{
		$post = $this->request->post();
		$rule = [
			'id|ID'      => 'require',
			'field|字段' => 'require',
			'value|值'   => 'require',
		];
		$this->validate($post, $rule);
		if (!in_array($post['field'], $this->allowModifyFields)) {
			$this->error('该字段不允许修改：' . $post['field']);
		}
		if ($post['id'] == AdminConstant::SUPER_ADMIN_ID && $post['field'] == 'status') {
			$this->error('超级管理员状态不允许修改');
		}
		$row = $this->model->find($post['id']);
		empty($row) && $this->error('数据不存在');
		try {
			$row->save([
				$post['field'] => $post['value'],
			]);
		}
		catch (\Exception $e) {
			$this->error($e->getMessage());
		}
		$this->success('保存成功');
	}
	
	
}
