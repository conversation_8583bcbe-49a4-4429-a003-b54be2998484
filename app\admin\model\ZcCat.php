<?php

namespace app\admin\model;

use app\admin\traits\ModelTrait;
use app\common\model\TimeModel;

class ZcCat extends TimeModel
{
	
	use ModelTrait;
	
	protected $name = "zc_cat";
	
	protected $deleteTime = "delete_time";
	
	public function creator()
	{
		return $this->belongsTo('\app\admin\model\SystemAdmin', 'create_by', 'id')
		            ->bind(['username']);
	}
	
	public static function getList()
	{
		return self::where('status', 1)
		           ->select();
	}
	
	
	public function getStatusList()
	{
		return [
			'0' => '禁用',
			'1' => '启用',
		];
	}
	
	public static function getSelectList()
	{
		$adminInfo = session('admin');
		$deptId    = $adminInfo['dept_id'];
		$where     = [];
		if ($adminInfo['id'] != 1) {
			$where[] = [
				'dept_id',
				'=',
				$deptId
			];
		}
		return self::where($where)
		           ->where('status', 1)
		           ->column('title', 'id');
	}
	
	
}