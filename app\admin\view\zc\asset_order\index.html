<div class="layuimini-container">
    <div class="layuimini-main">
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="{:auth('zc.asset_order/add')}"
               data-auth-edit="{:auth('zc.asset_order/edit')}"
               data-auth-check="{:auth('zc.asset_order/check')}"
               data-auth-delete="{:auth('zc.asset_order/delete')}"
               lay-filter="currentTable">
        </table>
    </div>
</div>
<style>
    .btn-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding: 5px 10px;
    }

    .btn-box a {
        width: 30%;
        margin-top: 0;
        margin-left: 10px;
    }

    .btn-box a:nth-child(3n+1) {
        margin-left: 0;
    }

    .btn-box a:nth-child(n+4) {
        margin-top: 10px;
    }
</style>
<script type="text/html" id="operatTpl">
    <div class="btn-box" style="">
        {if auth('zc.asset_order/edit')}
        <a class="layui-btn layui-btn-xs layui-btn-info" data-open="zc.asset_order/detail?id={{d.id}}"
           data-title="详情"
           data-full="false">详情</a>
        {/if}
        {{# if(d.is_check == 0){ }}
        {if auth('zc.asset_order/check')}
        <a class="layui-btn layui-btn-xs layui-btn-warm" data-open="zc.asset_order/check?id={{d.id}}" data-title="核实"
           data-full="false">核实</a>
        {/if}
        {{# } }}
        {{# if(d.is_check == 1){ }}
        {if auth('zc.asset_order_log/list')}
        <a class="layui-btn layui-btn-xs layui-btn-normal" data-open="zc.asset_order_log/list?oid={{d.id}}"
           data-title="收款" data-full="true">收款</a>
        <a class="layui-btn layui-btn-xs layui-btn-warm" data-open="zc.asset_order_event/index?oid={{d.id}}"
           data-title="特情处理" data-full="true">特情处理</a>
        {/if}
        {{# } }}
        {{# if(d.is_check < 2){ }}
        {if auth('zc.asset_order/zuofei')}
        <a class="layui-btn layui-btn-xs layui-btn-danger" data-request="zc.asset_order/zuofei?id={{d.id}}"
           data-title="作废"
           data-full="false">作废</a>
        {/if}
        {{# } }}
        {if auth('zc.asset_order/edit')}
        <a class="layui-btn layui-btn-xs layui-btn-success" data-open="zc.asset_order/edit?id={{d.id}}"
           data-title="编辑"
           data-full="false">编辑</a>
        {/if}
        {if auth('zc.asset_order/delete')}
        <a class="layui-btn layui-btn-xs layui-btn-danger" data-request="zc.asset_order/delete?id={{d.id}}"
           data-title="删除"
           data-full="false">删除</a>
        {/if}
    </div>

</script>