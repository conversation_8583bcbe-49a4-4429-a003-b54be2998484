<?php

namespace app\admin\traits;


use app\admin\model\SystemAdmin;

/**
 * @package app\shop\traits
 */
trait ModelTrait
{
	
	public function creator()
	{
		return $this->belongsTo(SystemAdmin::class, 'create_by', 'id')->bind(['creator_name' => 'username']);
	}
	
	public function saveFromCreateId($data)
	{
		$info = session('admin');
		if (empty($info)) {
			throw new \Exception('请先登录');
		}
		$data['create_by'] = $info['id'];
		return $this->save($data);
	}
	
	public function getStatusList(): array
	{
		return ['0' => '禁用', '1' => '启用',];
	}
	
	public function getInfo($id)
	{
		$where     = [];
		$dataScope = $this->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		return self::where('id', $id)
		           ->where($where)
		           ->find();
	}
	
}