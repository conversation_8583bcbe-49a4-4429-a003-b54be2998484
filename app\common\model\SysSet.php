<?php

namespace app\common\model;

use think\facade\Cache;

class SysSet extends TimeModel
{

    protected $name = "sys_set";

    protected $deleteTime = false;

    protected $schema = [
        'id' => 'int',
        'name' => 'string',
        'value' => 'string',
    ];

    protected $type = [
        'value' => 'serialize',
    ];

    public static function getValueByName(string $name)
    {
        //如果缓存存在，则返回缓存信息，如果不存在，再查询获取
        $value = [];
        if (empty($name)) return $value;
        $cacheStr = 'shop_sys_' . $name;
        $value = Cache::get($cacheStr);
        if (empty($value)) {
            $info = self::where('name', $name)->find();
            if (!empty($info)) {
                $value = $info->value;
                Cache::tag('shop_sys')->set($cacheStr, $value, 7200);
            }
        }
        return $value;
    }

    public static function setValueByName($data)
    {
        $value = $data['sys'] ?: [];
        if (empty($value)) return true;
        $name = $data['name_key'];
        $cacheStr = 'shop_sys_' . $name;
        $info = self::where('name', $name)->findOrEmpty();
        if ($info->isEmpty()) {
            $res = $info->save(['name'=>$name,'value' => $value]);
        } else {
            $info->value = $value;
            $res = $info->save();
        }
        Cache::tag('shop_sys')->set($cacheStr, $value, 7200);
        return $res;
    }

}