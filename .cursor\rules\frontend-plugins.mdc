---
description: 
globs: 
alwaysApply: false
---
# 前端插件目录结构

## 插件目录总览
项目使用了多个第三方前端库和插件，统一存放在 `public/static/plugs` 目录下：

- `easy-admin/`: 自定义管理界面框架，提供了表格、表单、弹窗等组件和功能
- `layui-v2.8.x/`: LayUI框架，提供UI组件库
- `lay-module/`: LayUI扩展模块
- `jquery-3.4.1/`: jQuery库
- `vue-2.6.10/`: Vue.js框架
- `echarts/`: 图表库
- `tinymce/`: 富文本编辑器
- `ckeditor4/`: 富文本编辑器备选
- `sortable/`: 拖拽排序插件
- `clipboard/`: 剪贴板操作插件
- `font-awesome-4.7.0/`: 图标库
- `require-2.3.6/`: RequireJS模块加载器
- `jq-module/`: jQuery扩展模块
- `air-datepicker/`: 日期选择器

## 核心文件
- [public/static/plugs/easy-admin/easy-admin.js](mdc:public/static/plugs/easy-admin/easy-admin.js): 自定义管理界面核心库
- [public/static/config-admin.js](mdc:public/static/config-admin.js): 前端模块配置和加载入口

## 模块依赖关系
项目使用RequireJS进行模块化管理，依赖关系配置在config-admin.js中，主要包括：
- jQuery作为基础库
- LayUI提供UI组件
- easy-admin整合了表格、表单等高级功能
- 其他插件按需加载

