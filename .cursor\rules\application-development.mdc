---
description: 
globs: 
alwaysApply: false
---
# 应用开发指南

## 控制器开发
- 所有控制器需要继承 [app/BaseController.php](mdc:app/BaseController.php)
- API返回统一使用json格式
- 权限验证通过中间件实现
- 参数验证使用validate验证器

## 模型开发
- 遵循"胖模型瘦控制器"原则
- 复杂查询应在模型中实现
- 使用模型关联处理表间关系
- 业务逻辑放在服务层处理

## 路由规则
- RESTful API路由定义在 [route/app.php](mdc:route/app.php)
- 路由使用资源路由方式定义
- 版本号通过路由前缀区分
- 后台路由添加admin前缀

## 配置说明
- 数据库配置在 [config/database.php](mdc:config/database.php)
- 应用配置在 [config/app.php](mdc:config/app.php)
- 缓存配置在 [config/cache.php](mdc:config/cache.php)
- 会话配置在 [config/session.php](mdc:config/session.php)

