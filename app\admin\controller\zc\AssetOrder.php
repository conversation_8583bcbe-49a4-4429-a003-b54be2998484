<?php

namespace app\admin\controller\zc;

use app\admin\model\ZcAssetOrder;
use app\admin\model\ZcAssetOrderLog;
use app\admin\traits\Curd;
use app\common\controller\AdminController;
use DateTime;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use jianyan\excel\Excel;
use think\App;
use think\facade\Log;
use zjkal\TimeHelper;

/**
 * @ControllerAnnotation(title="台账管理")
 */
class AssetOrder extends AdminController
{
	
	use Curd;
	
	public function __construct(App $app)
	{
		parent::__construct($app);
		
		$this->model = new \app\admin\model\ZcAssetOrder();
		
		$this->assign('getDeptSuoshuList', $this->model->getDeptSuoshuList());
		
		$this->assign('getDaiSuoshuList', $this->model->getDeptSuoshuList(1));
		
		$this->assign('getQiandingList', $this->model->getQiandingList());
		
		$this->assign('getPayTypeList', $this->model->getPayTypeList());
		
		$this->assign('getAssetList', $this->model->getAssetList());
		$this->assign('getZulinList', $this->model->getZulinList());
		
		
	}
	
	
	/**
	 * @NodeAnotation(title="列表")
	 */
	public function index()
	{
		if ($this->request->isAjax()) {
			if (input('selectFields')) {
				return $this->selectList();
			}
			list($page, $limit, $where) = $this->buildTableParames();
			
			foreach ($where as $k => $v) {
				if ($v[0] == 'end_date') {
					if ($v[2] == 1) {
						$where[] = [
							'zc_asset_order.end_date',
							'<',
							strtotime(date('Y-m-d'))
						];
					}
					else {
						$where[] = [
							'zc_asset_order.end_date',
							'>=',
							strtotime(date('Y-m-d'))
						];
					}
					array_splice($where, $k, 1);
				}
			}
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[] = [
					'zc_asset_order.create_by',
					'in',
					$dataScope
				];
			}
			$count = $this->model->withJoin([
				'asset',
				'creator',
				'zulin',
				'dept',
				'qiandings'
			], 'LEFT')
			                     ->where($where)
			                     ->count();
			$list  = $this->model->withJoin([
				'asset',
				'creator',
				'zulin',
				'dept',
				'qiandings',
			], 'LEFT')
			                     ->where($where)
			                     ->page($page, $limit)
			                     ->order($this->sort)
			                     ->select()
			                     ->hidden(['creator']);
			foreach ($list as $k => &$v) {
				$logs = ZcAssetOrderLog::where('asset_id', $v['asset_id'])
				                       ->where('order_id', $v['id'])
				                       ->order('end_date desc')
				                       ->findOrEmpty();
				if (!$logs->isEmpty()) {
					$startDate = new \DateTime($logs['end_date']);
				}
				else {
					$startDate = new \DateTime($v['start_date']);
				}
				$endDate     = new \DateTime($v['end_date']);
				$currentDate = new \DateTime();
				
				$interval  = $currentDate->diff($startDate);
				$years     = $interval->y;
				$totalDays = TimeHelper::diffDays($startDate->format('Y-m-d'), $endDate->format('Y-m-d'));
				$days      = TimeHelper::diffDays($currentDate->format('Y-m-d'), $endDate->format('Y-m-d'));
				// 当前日期距离$days是否超过一个月，如果不超过一个月，判断是否超过一周
				$v['progress_type'] = 0;
				if ($days <= 30) {
					$v['progress_type'] = 1;
					if ($days <= 7) {
						$v['progress_type'] = 2;
					}
				}
				// 计算start_date到当前日期的总天数
				$billDays = TimeHelper::diffDays($currentDate->format('Y-m-d'), $startDate->format('Y-m-d'));
				$bill     = $totalDays > 0
					? bcdiv($billDays, $totalDays, 2)
					: 0;
				if ($bill > 1) {
					$bill = 1;
				}
				$v['progress'] = bcmul($bill, 100);
				
				if ($currentDate >= $endDate) {
					continue;
				}
				
				if ($currentDate < $startDate) {
					$years = 0;
				}
				$initialPrice    = $v['total'];
				$priceGrowthRate = bcdiv($v['dizeng'], 100, 2);
				$currentPrice    = $initialPrice * pow((1 + $priceGrowthRate), $years);
				$v->save([
					'c_total' => $currentPrice,
				]);
				$v['start_date'] = $startDate->format('Y-m-d');
			}
			
			$data = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		return $this->fetch();
	}
	
	
	/**
	 * @NodeAnotation(title="添加")
	 */
	public function add()
	{
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$post['c_total'] = $post['total'];
				$save            = $this->model->saveFromCreateId($post);
			}
			catch (\Exception $e) {
				$this->error('保存失败:' . $e->getMessage());
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="编辑")
	 * @param $id
	 * @return
	 */
	public function edit($id)
	{
		$where     = [];
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->where($where)
		                   ->find($id);
		empty($row) && $this->error('数据不存在');
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $row->save($post);
			}
			catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('row', $row);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="详情")
	 * @param $id
	 * @return
	 */
	public function detail($id)
	{
		$where     = [];
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->where($where)
		                   ->find($id);
		empty($row) && $this->error('数据不存在');
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $row->save($post);
			}
			catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('row', $row);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="财务核实")
	 * @param $id
	 * @return
	 */
	public function check($id)
	{
		$where     = [];
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->where($where)
		                   ->find($id);
		empty($row) && $this->error('数据不存在');
		if ($row->is_check != 0) {
			$this->error('数据已审核或已作废');
		}
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $row->save([
					'is_check' => 1,
				]);
			}
			catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('row', $row);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="作废")
	 */
	public function zuofei($id)
	{
		$where     = [];
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->where('id', $id)
		                   ->where($where)
		                   ->findOrEmpty();
		$row->isEmpty() && $this->error('数据不存在');
		try {
			$save = $row->save([
				'is_check' => 2
			]);
		}
		catch (\Exception $e) {
			$this->error('删除失败');
		}
		$save
			? $this->success('删除成功')
			: $this->error('删除失败');
	}
	
	/**
	 * @NodeAnotation(title="删除")
	 */
	public function delete($id)
	{
		$where     = [];
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->whereIn('id', $id)
		                   ->where($where)
		                   ->select();
		$row->isEmpty() && $this->error('数据不存在');
		try {
			$save = $row->delete();
		}
		catch (\Exception $e) {
			$this->error('删除失败');
		}
		$save
			? $this->success('删除成功')
			: $this->error('删除失败');
	}
	
	/**
	 * @NodeAnotation(title="导出")
	 * @param $id
	 * @return
	 */
	public function export()
	{
		list($page, $limit, $where) = $this->buildTableParames();
		$header = [
			[
				'资产名称',
				'asset_title'
			],
			[
				'状态',
				'is_check_text'
			],
			[
				'所属单位',
				'dept_suoshu'
			],
			[
				'合同签订方',
				'qianding'
			],
			[
				'租用单位名称',
				'zulin'
			],
			[
				'合同编号',
				'contract_no'
			],
			[
				'押金',
				'ya_price'
			],
			[
				'面积',
				'area'
			],
			[
				'起租日期',
				'start_date'
			],
			[
				'到期日期',
				'end_date'
			],
			[
				'租赁单价',
				'price'
			],
			[
				'租赁总价',
				'total'
			],
			[
				'付费方式',
				'pay_type_text'
			],
			[
				'租金欠费金额',
				'qianfei'
			],
			[
				'上缴金额',
				'shangjiao'
			],
			[
				'自用金额',
				'ziyong'
			],
			[
				'经办人信息',
				'jingbanren'
			],
			[
				'滞纳金比例',
				'bill'
			],
			[
				'滞纳金金额/日',
				'day_price'
			],
			[
				'滞纳金总额',
				'zhinajin'
			],
			[
				'累计欠费',
				'leiji'
			],
			[
				'备注',
				'remark'
			],
		];
		$list   = $this->model->withJoin([
			'dept',
			'creator',
			'zulin',
			'asset',
			'qiandings'
		], 'LEFT')
		                      ->where($where)
		                      ->limit(100000)
		                      ->order('id', 'desc')
		                      ->order($this->sort)
		                      ->select();
		$data   = [];
		try {
			foreach ($list as $key => $item) {
				$statusArr  = [
					'未审核',
					'已审核',
					'已作废'
				];
				$dept       = $item->dept;
				$qiandings  = $item->qiandings;
				$statusText = $statusArr[$item->is_check] ?? '';
				$data[]     = [
					'asset_title'   => $item->asset->title,
					'status_text'   => $statusText,
					'dept_suoshu'   => $item->dept_suoshu == 0
						? '咸运集团'
						: $dept['name'],
					'qianding'      => $item->qianding == 0
						? '咸运集团'
						: $qiandings['name'],
					'zulin'         => $item->zulin->title,
					'contract_no'   => $item->contract_no,
					'ya_price'      => $item->ya_price,
					'area'          => $item->area,
					'start_date'    => $item->start_date,
					'end_date'      => $item->end_date,
					'price'         => $item->price,
					'total'         => $item->total,
					'pay_type_text' => $item->pay_type_text,
					'qianfei'       => $item->qianfei,
					'shangjiao'     => $item->shangjiao,
					'ziyong'        => $item->ziyong,
					'jingbanren'    => $item->jingbanren,
					'bill'          => $item->bill,
					'day_price'     => $item->day_price,
					'zhinajin'      => $item->zhinajin,
					'leiji'         => $item->leiji,
					'remark'        => $item->remark,
					'jiezu_date'    => $item->last_time,
				];
			}
		}
		catch (\exception $e) {
			Log::error($e->getMessage());
		}
		$fileName = '收款记录' . date('Y-m-d');
		return Excel::exportData($data, $header, $fileName, 'xlsx');
	}
	
	/**
	 * @NodeAnotation(title="日期")
	 */
	public function dlist()
	{
		if ($this->request->isAjax()) {
			if (input('selectFields')) {
				return $this->selectList();
			}
			list($page, $limit, $where) = $this->buildTableParames();
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[] = [
					'zc_asset_order.create_by',
					'in',
					$dataScope
				];
			}
			
			foreach ($where as $k => $v) {
				array_splice($where, $k, 1);
				if ($v[0] == 'end_date') {
					$where[] = [
						'end_date',
						'between',
						[
							strtotime($v[2][0]),
							strtotime($v[2][1])
						]
					];
				}
			}
			$count = $this->model->withJoin([
				'asset',
				'creator',
				'zulin',
				'dept'
			], 'LEFT')
			                     ->where($where)
			                     ->count();
			$list  = $this->model->withJoin([
				'asset',
				'creator',
				'zulin',
				'dept'
			], 'LEFT')
			                     ->where($where)
			                     ->page($page, $limit)
			                     ->order($this->sort)
			                     ->select()
			                     ->hidden(['creator']);
			$data  = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="单位")
	 */
	public function rlist()
	{
		if ($this->request->isAjax()) {
			if (input('selectFields')) {
				return $this->selectList();
			}
			list($page, $limit, $where) = $this->buildTableParames();
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[] = [
					'zc_asset_order.create_by',
					'in',
					$dataScope
				];
			}
			
			
			$count = $this->model->withJoin([
				'asset',
				'creator',
				'zulin',
				'dept'
			], 'LEFT')
			                     ->where($where)
			                     ->count();
			$list  = $this->model->withJoin([
				'asset',
				'creator',
				'zulin',
				'dept'
			], 'LEFT')
			                     ->where($where)
			                     ->page($page, $limit)
			                     ->order($this->sort)
			                     ->select()
			                     ->hidden(['creator']);
			$data  = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		return $this->fetch();
	}
	
	public function list()
	{
		$type     = input('type/d', 0);
		$dataType = input('data_type/s', '');
		$oldDataType = $dataType;
		if ($dataType == 'yingshou') {
			$dataType = 'daoqi';
		}
		$year = input('year/s', '');
		if ($this->request->isAjax()) {
			list($page, $limit, $where) = $this->buildTableParames();
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[] = [
					'zc_asset_order.create_by',
					'in',
					$dataScope
				];
			}
			if ($dataType == 'qianfei') {
				$where[] = [
					'zc_asset_order.leiji',
					'>',
					0
				];
			}
			$priceWhere = [];
			switch ($type) {
				case 0:
					$where[]      = [
						'zc_asset_order.end_date',
						'between',
						[
							strtotime(date('Y-m-d 00:00:00', time())),
							strtotime(date('Y-m-d 23:59:59', time()))
						]
					];
					$priceWhere[] = [
						'end_date',
						'between',
						[
							strtotime(date('Y-m-d 00:00:00', time())),
							strtotime(date('Y-m-d 23:59:59', time()))
						]
					];
					break;
				case 1:
					$firstDayOfMonth = date('Y-m-01 00:00:00');
					// 本月最后一天
					$today        = strtotime(date('Y-m-t') . ' 23:59:59');
					$where[]      = [
						'zc_asset_order.end_date',
						'between',
						[
							strtotime($firstDayOfMonth),
							$today
						]
					];
					$priceWhere[] = [
						'end_date',
						'between',
						[
							strtotime($firstDayOfMonth),
							$today
						]
					];
					break;
				case 2:
					$year           = $year
						?: date('Y');
					$firstDayOfYear = date('Y-01-01 00:00:00');
					$lastDayOfYear  = date($year . '-12-31 23:59:59');
					$where[]        = [
						'zc_asset_order.end_date',
						'between',
						[
							strtotime($firstDayOfYear),
							strtotime($lastDayOfYear)
						]
					];
					$priceWhere[]   = [
						'end_date',
						'between',
						[
							strtotime($firstDayOfYear),
							strtotime($lastDayOfYear)
						]
					];
					break;
				case 3:
					$priceWhere = [];
				case 4:
					// 根据$year获取第一天和那年的最后一天
					$firstDayOfYear = date($year . '-01-01 00:00:00');
					$lastDayOfYear  = date($year . '-12-31 23:59:59');
					$where[]        = [
						'zc_asset_order.end_date',
						'between',
						[
							strtotime($firstDayOfYear),
							strtotime($lastDayOfYear)
						]
					];
					$priceWhere[]   = [
						'end_date',
						'between',
						[
							strtotime($firstDayOfYear),
							strtotime($lastDayOfYear)
						]
					];
			}
			
			
			/*$count    = $this->model->withJoin([
				'asset',
				'creator',
				'zulin',
				'dept'
			], 'LEFT')
			                        ->where($where)
			                        ->count();*/
			$list     = $this->model->withJoin([
				'asset',
				'creator',
				'zulin',
				'dept'
			], 'LEFT')
			                        ->where($where)
				//			                        ->page($page, $limit)
				                    ->order($this->sort)
			                        ->select()
			                        ->hidden(['creator']);
			$listData = [];
			foreach ($list as $k => $item) {
				$orderWhere = [
					[
						'asset_id',
						'=',
						$item->asset_id
					],
					[
						'order_id',
						'=',
						$item->id
					]
				];
				$orderWhere = array_merge($orderWhere, $priceWhere);
				$shishou    = ZcAssetOrderLog::where($orderWhere)
				                             ->sum('price');
				if ($dataType == 'shishou' && $shishou <= 0) {
					continue;
				}
				$list[$k]['shishou'] = $shishou;
				
				// 计算应收金额
				if (!empty($year)) {
					// 使用新的年度应收计算方法
					$list[$k]['yingshou'] = ZcAssetOrder::calcYearlyYingshou($year, [$item->id]);
				}
				else {
					// 保持原有逻辑
					$payType = $item->pay_type;
					$price   = $item->price;
					$total   = 0;
					switch ($payType) {
						case 0:
							$total = 1;
							break;
						case 1:
							$total = 4;
							break;
						case 2:
							$total = 6;
							break;
						case 3:
							$total = 12;
					}
					$list[$k]['yingshou'] = bcmul($price, (string)$total, 2);
				}
				
				$listData[] = $list[$k];
			}
			$count = count($listData);
			$data  = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $listData,
			];
			return json($data);
		}
		$this->assign('data_type', $oldDataType);
		$this->assign('year', $year);
		$this->assign('type', $type);
		return $this->fetch();
	}
	
}
