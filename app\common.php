<?php
// 应用公共文件

use app\admin\model\SystemConfig;
use app\common\model\SysSet;
use app\common\service\AuthService;
use EasyAdmin\tool\CommonTool;
use think\facade\Cache;
use think\facade\Request;

if (!function_exists('__url')) {

    /**
     * 构建URL地址
     * @param string $url
     * @param array $vars
     * @param bool $suffix
     * @param bool $domain
     * @return string
     */
    function __url(string $url = '', array $vars = [], $suffix = true, $domain = false)
    {
        return url($url, $vars, $suffix, $domain)->build();
    }
}

if (!function_exists('password')) {

    /**
     * 密码加密算法
     * @param $value
     * @return mixed
     */
    function password($value)
    {
        $value = sha1('blog_') . md5($value) . md5('_encrypt') . sha1($value);
        return sha1($value);
    }

}

if (!function_exists('xdebug')) {

    /**
     * debug调试
     * @param string|array $data 打印信息
     * @param string $type 类型
     * @param string $suffix 文件后缀名
     * @param bool $force
     * @param null $file
     */
    function xdebug($data, $type = 'xdebug', $suffix = null, $force = false, $file = null)
    {
        !is_dir(runtime_path() . 'xdebug/') && mkdir(runtime_path() . 'xdebug/');
        if (is_null($file)) {
            $file = is_null($suffix) ? runtime_path() . 'xdebug/' . date('Ymd') . '.txt' : runtime_path() . 'xdebug/' . date('Ymd') . "_{$suffix}" . '.txt';
        }
        file_put_contents($file, "[" . date('Y-m-d H:i:s') . "] " . "========================= {$type} ===========================" . PHP_EOL, FILE_APPEND);
        $str = (is_string($data) ? $data : (is_array($data) || is_object($data)) ? print_r($data, true) : var_export($data, true)) . PHP_EOL;
        $force ? file_put_contents($file, $str) : file_put_contents($file, $str, FILE_APPEND);
    }
}


function actionIndex($string, $start = 1, $length = 0, $re = '*')
{
    if (empty($string)) return $string;
    $strLen = mb_strlen($string);
    if ($strLen < 2) return $string;
    if ($strLen == 2) {
        $end = 2;
    } else {
        $end = $length === 0 ? $strLen - 1 : $start + $length;
    }
    $str_arr = [];
    for ($i = 0; $i < $strLen; $i++) {
        if ($i >= $start && $i < $end)
            $str_arr[] = $re;
        else
            $str_arr[] = mb_substr($string, $i, 1);
    }
    return implode('', $str_arr);
}

if (!function_exists('sysconfig')) {

    /**
     * 获取系统配置信息
     * @param $group
     * @param null $name
     * @return array|mixed
     */
    function sysconfig($group, $name = null)
    {
        $where = ['group' => $group];
        $value = empty($name) ? Cache::get("sysconfig_{$group}") : Cache::get("sysconfig_{$group}_{$name}");
        if (empty($value)) {
            if (!empty($name)) {
                $where['name'] = $name;
                $value = SystemConfig::where($where)->value('value');
                Cache::tag('sysconfig')->set("sysconfig_{$group}_{$name}", $value, 3600);
            } else {
                $value = SystemConfig::where($where)->column('value', 'name');
                Cache::tag('sysconfig')->set("sysconfig_{$group}", $value, 3600);
            }
        }
        return $value;
    }
}

if (!function_exists('getShopSysByName')) {

    /**
     * 获取自定义配置信息
     * @param $name
     * @return array|mixed
     */
    function getShopSysByKey($name, $key)
    {
        $info = SysSet::getValueByName($name);
        return $info[$key] ?? '';
    }
}

if (!function_exists('array_format_key')) {

    /**
     * 二位数组重新组合数据
     * @param $array
     * @param $key
     * @return array
     */
    function array_format_key($array, $key)
    {
        $newArray = [];
        foreach ($array as $vo) {
            $newArray[$vo[$key]] = $vo;
        }
        return $newArray;
    }

}



if (!function_exists('shopAuth')) {

    /**
     * auth权限验证
     * @param $node
     * @return bool
     */
    function shopAuth($node = null)
    {
        return true;
    }

}

if (!function_exists('parseNodeStr')) {
    /**
     * 驼峰转下划线规则
     * @param string $node
     * @return string
     */
    function parseNodeStr($node)
    {
        $array = explode('/', $node);
        foreach ($array as $key => $val) {
            if ($key == 0) {
                $val = explode('.', $val);
                foreach ($val as &$vo) {
                    $vo = CommonTool::humpToLine(lcfirst($vo));
                }
                $val = implode('.', $val);
                $array[$key] = $val;
            }
        }
        $node = implode('/', $array);
        return $node;
    }
}

/**
 * todo 模型内获取图片或文件的完整Url地址
 * @param string $value 资源地址
 * @param string $type 资源类型
 * @return string
 */
function getModelAttrUrl(string $value, string $type): string
{
    return $type == 'local' ? $value : Request::domain() . $value;
}

function getFileUrl($path)
{
    $filePath = app()->getRootPath() . 'public' . $path;

    if (is_file($filePath)) {
        return Request::domain() . DS . $path;
    }
    return $path;
}

//获取多张
function getImgUrlList($list)
{
    foreach ($list as $key => $item) {
        $images = explode('|', $item['swiper_image']);

        foreach ($images as $k => $swiper) {
            $images[$k] = getFileUrl($swiper);
        }
        $list[$key]['swiper_image'] = $images;
        $schoolImg = explode('|', $item['img']);
        foreach ($schoolImg as $k => $school) {
            $schoolImg[$k] = getFileUrl($school);
        }
        $list[$key]['img'] = $schoolImg;
    }
    return $list;
}

//获取单张
function getImgUrl($info)
{
    $images = explode('|', $info['swiper_image']);
    foreach ($images as &$item) {
        $item = getFileUrl($item);
    }
    $info['swiper_image'] = $images;
    $schoolImg = explode('|', $info['img']);
    foreach ($schoolImg as &$item) {
        $item = getFileUrl($item);
    }
    $info['img'] = $schoolImg;
    return $info;
}

if (!function_exists('httpRequest')) {
    function httpRequest($url, $data, $header = [], $type = '', $method = 'POST', $timeout = 30)
    {
        $cl = curl_init();
        curl_setopt($cl, CURLOPT_URL, $url);
        if (1 == strpos("$" . $url, "https://")) {
            curl_setopt($cl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cl, CURLOPT_SSL_VERIFYHOST, false);
        }
        curl_setopt($cl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($cl, CURLOPT_CUSTOMREQUEST, $method); //设置请求方式
        if (empty($header)) {
            $header = ['Content-Type: application/x-www-form-urlencoded'];
        }
        curl_setopt($cl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($cl, CURLOPT_FAILONERROR, false);
        curl_setopt($cl, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($cl, CURLOPT_POSTFIELDS, $data);
        $content = curl_exec($cl);
        curl_close($cl);
        return $content;
    }
}

function encodeStr($string = '', $strKey = 'hunsha')
{

    $strArr = str_split(base64_encode($string));

    $strCount = count($strArr);

    foreach (str_split($strKey) as $key => $value)

        $key < $strCount && $strArr[$key] .= $value;

    return str_replace(array('=', '+', '/'), array('O0O0O', 'o000o', 'oo00o'), join('', $strArr));

}

function decodeStr($string = '', $strKey = 'hunsha')
{

    $strArr = str_split(str_replace(array('O0O0O', 'o000o', 'oo00o'), array('=', '+', '/'), $string), 2);

    $strCount = count($strArr);

    foreach (str_split($strKey) as $key => $value)

        $key <= $strCount && $strArr[$key][1] === $value && $strArr[$key] = $strArr[$key][0];

    return base64_decode(join('', $strArr));

}

//function createOrderNum($prefix = 'D'): string
//{
//    return $prefix . (strtotime(date('YmdHis', time()))) . substr(microtime(), 2, 6) . sprintf('%08d', rand(0, 99999999));
//}

// 生成订单号：年月日时分秒+6位随机数
function createOrderNum($prefix = 'D'): string
{
	return $prefix . date('YmdHis', time()) . substr(microtime(), 2, 6) . sprintf('%08d', rand(0, 99999999));
}


function hex2rgb($hex) {
	// 使用正则表达式验证十六进制颜色代码
	if (!preg_match('/^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})$/i', $hex, $matches)) {
		return false;
	}
	
	// 转换为十进制并返回RGB数组
	return array(
		hexdec($matches[1]),
		hexdec($matches[2]),
		hexdec($matches[3])
	);
}