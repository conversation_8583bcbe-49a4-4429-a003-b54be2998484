<?php

namespace app\common\model;

use app\shop\model\ShopAdminMini;

class WxMiniFans extends TimeModel
{
	
	protected $name = "wx_mini_fans";
	
	protected $deleteTime = "delete_time";
	
	public function getIsStatusList()
	{
		return [
			'0' => '否',
			'1' => '是'
		];
	}
	
	public function getLevelList()
	{
		return [
			'0' => '普通',
			'1' => '员工',
			'2' => '子管理员',
			'3' => '黑名单',
		];
	}
	
	public static function getEmpList($shopId)
	{
		$user     = session('shop');
		$where    = [];
		$modelObj = new self();
		if (!$user['is_super']) {
			$miniIds  = ShopAdminMini::where('admin_id', '=', $user['id'])
			                         ->column('mini_id');
			$where[]  = [
				'id',
				'in',
				$miniIds
			];
			$modelObj = $modelObj->where($where);
		}
		return $modelObj->where('shop_id', $shopId)
		                ->where('level', 1)
		                ->where('')
		                ->field('id,remark_name')
		                ->select();
	}
	
	public static function getEmpSelectList($shopId)
	{
		return self::where('level', 1)
		           ->where('shop_id', $shopId)
		           ->column('remark_name', 'id');
	}
	
	public static function getListByShopId($shopId)
	{
		return self::where('shop_id', $shopId)
		           ->where('level', 1)
		           ->field('id as value,remark_name as label')
		           ->select();
	}
	
}