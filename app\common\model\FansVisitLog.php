<?php

namespace app\common\model;

use app\shop\model\ShopCat;

class FansVisitLog extends TimeModel
{
	
	protected $name = "fans_visit_log";
	
	protected $deleteTime = "delete_time";
	
	public function cat()
	{
		return $this->belongsTo(ShopNav::class, 'cat_id', 'id');
	}
	
	public function content()
	{
		return $this->belongsTo(ShopContent::class, 'content_id', 'id');
	}
	
	
	public static function userWatch($userInfo, $shopId, $params)
	{
		$fansInfo = WxMiniFans::where('id', '=', $userInfo['f_id'])
		                      ->where('shop_id', $shopId)
		                      ->findOrEmpty();
		if ($fansInfo->isEmpty()) {
			return 'success';
		}
		if (empty($fansInfo->nickname) || empty($fansInfo->mobile)) {
			return 'success';
		}
		try {
			$times = $params['times'] ?? 0;
			if ($times <= 0) {
				throw new \Exception('观看时长不能小于0');
			}
			// 写入观看记录
			$watchModel = new self();
			return $watchModel->save([
				'shop_id'    => $shopId,
				'cat_id'     => $params['cat_id'] ?? 0,
				'content_id' => $params['content_id'] ?? 0,
				'type'       => $params['type'] ?? '',
				'fid'        => $userInfo['f_id'],
				'times'      => $times,
			]);
		}
		catch (\Exception $e) {
			throw new \Exception($e->getMessage());
		}
	}
}