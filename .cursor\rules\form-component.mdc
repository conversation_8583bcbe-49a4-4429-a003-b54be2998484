---
description: 
globs: 
alwaysApply: false
---
# 表单组件使用指南

表单是后台管理系统的重要部分，主要由 [easy-admin.js](mdc:public/static/plugs/easy-admin/easy-admin.js) 中的表单相关功能提供，基于LayUI form组件进行扩展。

## 基础表单结构

```html
<form class="layui-form" lay-filter="form-filter">
    <div class="layui-form-item">
        <label class="layui-form-label">标题</label>
        <div class="layui-input-block">
            <input type="text" name="title" class="layui-input" lay-verify="required" placeholder="请输入标题">
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-block">
            <input type="radio" name="status" value="1" title="启用" checked>
            <input type="radio" name="status" value="0" title="禁用">
        </div>
    </div>
    
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="saveForm">立即提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</form>
```

## 表单验证

LayUI表单验证规则：

```html
<!-- 必填项 -->
<input lay-verify="required" lay-reqtext="请填写标题">

<!-- 电话号码 -->
<input lay-verify="phone" placeholder="请输入电话号码">

<!-- 邮箱 -->
<input lay-verify="email" placeholder="请输入邮箱">

<!-- 数字 -->
<input lay-verify="number" placeholder="请输入数字">

<!-- 自定义验证组合 -->
<input lay-verify="required|phone" placeholder="请输入手机号码">
```

## 上传组件

Easy-Admin封装的上传组件：

```html
<!-- 单图上传 -->
<div class="layui-form-item">
    <label class="layui-form-label">图片</label>
    <div class="layui-input-block">
        <input name="image" data-upload="image" data-upload-number="one" class="layui-input">
    </div>
</div>

<!-- 多图上传 -->
<div class="layui-form-item">
    <label class="layui-form-label">图片集</label>
    <div class="layui-input-block">
        <input name="images" data-upload="image" data-upload-number="more" data-upload-sign="|" class="layui-input">
    </div>
</div>

<!-- 文件上传 -->
<div class="layui-form-item">
    <label class="layui-form-label">附件</label>
    <div class="layui-input-block">
        <input name="attachment" data-upload="file" data-upload-exts="doc|docx|xls|xlsx|pdf" class="layui-input">
    </div>
</div>
```

## 富文本编辑器

```html
<div class="layui-form-item">
    <label class="layui-form-label">内容</label>
    <div class="layui-input-block">
        <textarea name="content" class="editor"></textarea>
    </div>
</div>
```

## 下拉选择

```html
<!-- 静态下拉 -->
<div class="layui-form-item">
    <label class="layui-form-label">分类</label>
    <div class="layui-input-block">
        <select name="category_id">
            <option value="">请选择分类</option>
            <option value="1">分类一</option>
            <option value="2">分类二</option>
        </select>
    </div>
</div>

<!-- 动态下拉 -->
<div class="layui-form-item">
    <label class="layui-form-label">分类</label>
    <div class="layui-input-block">
        <select name="category_id" data-select="{:url('ajax/getCategory')}" data-fields="id,name" data-value="{$data.category_id|default=''}"></select>
    </div>
</div>
```

## 日期选择

```html
<!-- 日期选择 -->
<div class="layui-form-item">
    <label class="layui-form-label">日期</label>
    <div class="layui-input-block">
        <input type="text" name="date" data-date class="layui-input">
    </div>
</div>

<!-- 日期范围 -->
<div class="layui-form-item">
    <label class="layui-form-label">日期范围</label>
    <div class="layui-input-block">
        <input type="text" name="date_range" data-date data-date-range="~" class="layui-input">
    </div>
</div>

<!-- 时间选择 -->
<div class="layui-form-item">
    <label class="layui-form-label">时间</label>
    <div class="layui-input-block">
        <input type="text" name="time" data-date data-date-type="time" class="layui-input">
    </div>
</div>
```

## 表单提交

初始化表单并监听提交事件：

```javascript
// 页面控制器
define(["jquery", "easy-admin"], function ($, admin) {
    var Controller = {
        add: function () {
            admin.listen(function (data) {
                // 表单数据预处理
                return data;
            });
        }
    };
    return Controller;
});
```

表单提交按钮：

```html
<button class="layui-btn" lay-submit lay-filter="saveForm" data-refresh="false">立即提交</button>
```

也可以通过JS触发表单提交：

```javascript
admin.api.form(url, data, function(res) {
    // 成功回调
    admin.msg.success(res.msg, function() {
        admin.api.closeCurrentOpen({refreshTable: true});
    });
});
```

