<?php

namespace app\admin\controller\system;

use app\admin\model\SystemDept;
use app\common\controller\AdminController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use think\App;

/**
 * @ControllerAnnotation(title="部门管理")
 */
class Dept extends AdminController
{

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->model = new SystemDept();
		
		$this->assign('getStatusList',$this->model->getStatusList());
        
    }
	
	/**
	 * @NodeAnotation(title="列表")
	 */
	public function index()
	{
		if ($this->request->isAjax()) {
			if (input('selectFields')) {
				return $this->selectList();
			}
			list($page, $limit, $where) = $this->buildTableParames();
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[]   = [
					'create_by',
					'in',
					$dataScope
				];
			}
			$count = $this->model->where($where)
				->count();
			$list = $this->model->with(['creator'])->where($where)
				->page($page, $limit)
				->order($this->sort)
				->select();
			$data = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="添加")
	 */
	public function add()
	{
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $this->model->saveFromCreateId($post);
			} catch (\Exception $e) {
				$this->error('保存失败:'.$e->getMessage());
			}
			$save ? $this->success('保存成功') : $this->error('保存失败');
		}
		$pidMenuList = $this->model->getPidMenuList();
		$this->assign('pidMenuList', $pidMenuList);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="编辑")
	 * @param $id
	 * @return
	 */
	public function edit($id)
	{
		$dataScope = $this->model->getThisModelDataScope();
		$where = [];
		if ($dataScope !== true) {
			$where[]   = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->where($where)->find($id);
		empty($row) && $this->error('数据不存在');
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $row->saveFromCreateId($post);
			} catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save ? $this->success('保存成功') : $this->error('保存失败');
		}
		$pidMenuList = $this->model->getPidMenuList();
		$this->assign('pidMenuList', $pidMenuList);
		$this->assign('row', $row);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="删除")
	 */
	public function delete($id)
	{
		$dataScope = $this->model->getThisModelDataScope();
		$where = [];
		if ($dataScope !== true) {
			$where[]   = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->whereIn('id', $id)->where($where)->select();
		$row->isEmpty() && $this->error('数据不存在');
		try {
			$save = $row->delete();
		} catch (\Exception $e) {
			$this->error('删除失败');
		}
		$save ? $this->success('删除成功') : $this->error('删除失败');
	}

    
}