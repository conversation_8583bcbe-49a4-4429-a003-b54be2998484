<?php


namespace app\admin\model;


use app\admin\traits\ModelTrait;
use app\common\model\TimeModel;

class SystemAuth extends TimeModel
{
	use ModelTrait;
	
	protected $deleteTime = 'delete_time';
	
	public static function getScopeList()
	{
		return [
			1 => '全部数据',
			3 => '本单位',
			4 => '仅本人',
		];
	}
	
	public function getAllNodeListByAdminId($authId)
	{
		
		$checkNodeList = (new SystemAuthNode())->where('auth_id', $authId)
		                                       ->column('node_id');
		// 权限继承
		$userInfo = session('admin');
		$authIds  = $userInfo['auth_ids'] ?? [];
		if (empty($authIds)) {
			if ($userInfo['id'] == 1) {
				$nodes = SystemNode::column('id');
			}
			else {
				return [];
			}
		}
		else {
			$nodes = SystemAuthNode::where('auth_id', $authIds)
			                       ->column('node_id');
		}
		
		$nodelList = (new SystemNode())->whereIn('id', $nodes)
		                               ->where('is_auth', 1)
		                               ->field('id,node,title,type,is_auth')
		                               ->select()
		                               ->toArray();
		
		$newNodeList = [];
		foreach ($nodelList as $vo) {
			if ($vo['type'] == 1) {
				$vo            = array_merge($vo, [
					'field'  => 'node',
					'spread' => true
				]);
				$vo['checked'] = false;
				$vo['title']   = "{$vo['title']}【{$vo['node']}】";
				$children      = [];
				foreach ($nodelList as $v) {
					if ($v['type'] == 2 && strpos($v['node'], $vo['node'] . '/') !== false) {
						$v            = array_merge($v, [
							'field'  => 'node',
							'spread' => true
						]);
						$v['checked'] = in_array($v['id'], $checkNodeList);
						$v['title']   = "{$v['title']}【{$v['node']}】";
						$children[]   = $v;
					}
				}
				!empty($children) && $vo['children'] = $children;
				$newNodeList[] = $vo;
			}
		}
		return $newNodeList;
	}
	
	/**
	 * 根据角色ID获取授权节点
	 *
	 * @param $authId
	 * @return array
	 * @throws \think\db\exception\DataNotFoundException
	 * @throws \think\db\exception\DbException
	 * @throws \think\db\exception\ModelNotFoundException
	 */
	public function getAuthorizeNodeListByAdminId($authId)
	{
		$checkNodeList = (new SystemAuthNode())->where('auth_id', $authId)
		                                       ->column('node_id');
		$systemNode    = new SystemNode();
		$nodelList     = $systemNode->where('is_auth', 1)
		                            ->field('id,node,title,type,is_auth')
		                            ->select()
		                            ->toArray();
		$newNodeList   = [];
		foreach ($nodelList as $vo) {
			if ($vo['type'] == 1) {
				$vo            = array_merge($vo, [
					'field'  => 'node',
					'spread' => true
				]);
				$vo['checked'] = false;
				$vo['title']   = "{$vo['title']}【{$vo['node']}】";
				$children      = [];
				foreach ($nodelList as $v) {
					if ($v['type'] == 2 && strpos($v['node'], $vo['node'] . '/') !== false) {
						$v            = array_merge($v, [
							'field'  => 'node',
							'spread' => true
						]);
						$v['checked'] = in_array($v['id'], $checkNodeList)
							? true
							: false;
						$v['title']   = "{$v['title']}【{$v['node']}】";
						$children[]   = $v;
					}
				}
				!empty($children) && $vo['children'] = $children;
				$newNodeList[] = $vo;
			}
		}
		return $newNodeList;
	}
	
	public function getUserDataScopeList()
	{
		$userInfo = session('admin');
		$userId   = $userInfo['id'];
		if ($userId == 1) {
			return true;
		}
		// 获取用户数据权限
		$userInfo = SystemAdmin::getAdminInfoByShopId($userId);
		if ($userInfo->isEmpty()) {
			throw new \Exception('用户不存在');
		}
		$authIds  = $userInfo['auth_ids'] ?? [];
		$authInfo = self::whereIn('id', $authIds)
		                ->findOrEmpty();
		if ($authInfo->isEmpty()) {
			throw new \Exception('权限不存在');
		}
		$dataScope = $authInfo['data_scope'];
		switch ($dataScope) {
			case 1:
				// 找出所有id
				$creatorIds = SystemAdmin::where('status', 1)
				                         ->column('id');
				break;
			case 2:
				$deptIds    = SystemDept::where('parent_id', $userInfo['dept_id'])
				                        ->column('id');
				$deptIds[]  = $userInfo['depart_id'];
				$creatorIds = SystemAdmin::whereIn('dept_id', $deptIds)
				                         ->where('status', 1)
				                         ->column('id');
				/*case 2:
				$creatorIds = ShopAdmin::where('shop_id', $shopId)
				                       ->whereIn('dept_id', $deptIds)
				                       ->where('status', 1)
				                       ->column('id');
				break;*/
				break;
			case 3:
				$creatorIds = SystemAdmin::where('dept_id', $userInfo['dept_id'])
				                         ->where('status', 1)
				                         ->column('id');
				break;
			default:
				$creatorIds = [$userId];
				break;
		}
		return $creatorIds;
	}
	
}