<?php

namespace app\admin\model;

use app\admin\traits\ModelTrait;
use app\common\model\TimeModel;

class SystemDept extends TimeModel
{
	
	use ModelTrait;
	
	protected $name = "system_dept";
	
	protected $deleteTime = "delete_time";
	
	public function getDeptList()
	{
		
		$where = [
			[
				'status',
				'=',
				1
			],
		];
		$dataScope = $this->getThisModelDataScope();
		if ($dataScope !== true && !empty($dataScope)) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$list = $this->field('id,parent_id,name as title')
		             ->where($where)
		             ->order('sort desc')
		             ->select()
		             ->toArray();
		// 格式：[{id:'',title:'',children:[]}]
		$data = $this->buildTree($list);
		return $data;
		/*return array_merge([
			[
				'id'    => 0,
				'title' => '全部',
			]
		], $data);*/
	}
	
	function buildTree(array $items, $parentId = 0): array
	{
		$tree = [];
		foreach ($items as $item) {
			if ($item['parent_id'] == $parentId) {
				$children = $this->buildTree($items, $item['id']);
				if (!empty($children)) {
					$item['children'] = $children;
				}
				$tree[] = $item;
			}
		}
		return $tree;
	}
	
	
	public function getPidMenuList()
	{
		
		$where = [
			[
				'status',
				'=',
				1
			],
		];
		/*$dataScope = $this->getThisModelDataScope();
		if (!empty($dataScope)) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}*/
		$list        = $this->field('id,parent_id,name')
		                    ->where($where)
		                    ->order('sort asc')
		                    ->select()
		                    ->toArray();
		$pidMenuList = $this->buildPidMenu(0, $list);
		return array_merge([
			[
				'id'        => 0,
				'parent_id' => 0,
				'name'      => '顶级菜单',
			]
		], $pidMenuList);
	}
	
	protected function buildPidMenu($pid, $list, $level = 0)
	{
		$newList = [];
		foreach ($list as $vo) {
			if ($vo['parent_id'] == $pid) {
				$level++;
				foreach ($newList as $v) {
					if ($vo['parent_id'] == $v['parent_id'] && isset($v['level'])) {
						$level = $v['level'];
						break;
					}
				}
				$vo['level'] = $level;
				if ($level > 1) {
					$repeatString = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
					$markString   = str_repeat("{$repeatString}├{$repeatString}", $level - 1);
					$vo['name']   = $markString . $vo['name'];
				}
				$newList[] = $vo;
				$childList = $this->buildPidMenu($vo['id'], $list, $level);
				!empty($childList) && $newList = array_merge($newList, $childList);
			}
			
		}
		return $newList;
	}
	
	
}