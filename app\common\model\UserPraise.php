<?php

namespace app\common\model;

class UserPraise extends TimeModel
{

    protected $name = "user_praise";

    protected $deleteTime = "delete_time";
    
    public function getCheckedStatusList()
    {
        return ['0'=>'否','1'=>'是',];
    }

    public function getUserTitleAttr($value)
    {
        return htmlspecialchars_decode($value);
    }

    public function praise()
    {
        return $this->belongsTo(Praise::class,'praise_id');
    }

    public function prize()
    {
        return $this->belongsTo(Prize::class,'prize_id');
    }

}