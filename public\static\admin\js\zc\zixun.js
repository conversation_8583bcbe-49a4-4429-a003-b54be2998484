define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'zc.zixun/index',
        add_url: 'zc.zixun/add',
        edit_url: 'zc.zixun/edit',
        delete_url: 'zc.zixun/delete',
        export_url: 'zc.zixun/export',
        modify_url: 'zc.zixun/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    {type: 'nmbers', title: '序号'},
                    {field: 'asset.title', title: '资产名称'},
                    {field: 'name', title: '咨询人姓名'},
                    {field: 'mobile', title: '咨询人电话'},
                    {field: 'create_time',search:'date_range', title: '创建时间'},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});