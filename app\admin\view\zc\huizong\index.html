<div class="layuimini-container">
    <div class="layuimini-main">

        <div style="padding: 15px;">

            <div style="margin-right: 15px;">

                <div class="layui-card">
                    <div class="layui-card-header" style="font-size: 18px;font-weight: bold;color: #333;">数据统计</div>
                    <div class="layui-card-body">
                        <form class="layui-form">
                            <div class="layui-form-item" style="margin-bottom: 20px;">
                                <div class="layui-inline">
                                    <label class="layui-form-label">年份选择：</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="year_select" class="layui-input" placeholder="请选择年份" readonly>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <div class="layui-tab layui-tab-card" lay-filter="test-hash">
                            <ul class="layui-tab-title">
                                <li class="layui-this" id="type0" lay-id="0">今日</li>
                                <li id="type1" lay-id="1">本月</li>
                                <li id="type2" lay-id="2">今年</li>
                                <li id="type3" lay-id="3">汇总</li>
                                <li id="type4" lay-id="4"></li>
                            </ul>

                            <div class="layui-tab-content">

                                <div class="layui-tab-item layui-show">
                                    <div class="welcome-module">
                                        <div style="display: flex;justify-content: space-around;width: 100%;"
                                             id="type_box">

                                            <div style="width: 23%;padding: 10px;">
                                                <div style="background-color: #F8F8F8;padding: 10px 20px;border-radius: 5px;text-align: center;">
                                                    <div style="display: flex;align-items: center;justify-content: center;margin-bottom: 10px;">
                                                        <div style="background: url('__STATIC__/admin/images/1.png') no-repeat;background-size: 100% 100%;height: 30px;width: 30px;"></div>
                                                        <h5 style="margin: 0 0 0 10px;">应收</h5>
                                                    </div>
                                                    <div style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                                                        <h2 class="no-margins yingshou" id="yingshou_click"
                                                        style="color: orange;cursor: pointer;font-size: 20px;">0</h2>
                                                    </div>
                                                </div>
                                            </div>

                                            <div style="width: 23%;padding: 10px;">
                                                <div style="background-color: #F8F8F8;padding: 10px 20px;border-radius: 5px;text-align: center;">
                                                    <div style="display: flex;align-items: center;justify-content: center;margin-bottom: 10px;">
                                                        <div style="background: url('__STATIC__/admin/images/2.png') no-repeat;background-size: 100% 100%;height: 30px;width: 30px;"></div>
                                                        <h5 style="margin: 0 0 0 10px;">实收</h5>
                                                    </div>
                                                    <div style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                                                        <h2 class="no-margins shishou" id="shishou_click"
                                                        style="color: orange;cursor: pointer;font-size: 20px;">0</h2>
                                                    </div>
                                                </div>
                                            </div>

                                            <div style="width: 23%;padding: 10px;">
                                                <div style="background-color: #F8F8F8;padding: 10px 20px;border-radius: 5px;text-align: center;">
                                                    <div style="display: flex;align-items: center;justify-content: center;margin-bottom: 10px;">
                                                        <div style="background: url('__STATIC__/admin/images/34.png') no-repeat;background-size: 100% 100%;height: 30px;width: 30px;"></div>
                                                        <h5 style="margin: 0 0 0 10px;">欠费金额</h5>
                                                    </div>
                                                    <div style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                                                        <h2 class="no-margins qianfei" id="qianfei_click"
                                                        style="color: orange;cursor: pointer;font-size: 20px;">0</h2>
                                                    </div>
                                                </div>
                                            </div>

                                            <div style="width: 23%;padding: 10px;">
                                                <div style="background-color: #F8F8F8;padding: 10px 20px;border-radius: 5px;text-align: center;">
                                                    <div style="display: flex;align-items: center;justify-content: center;margin-bottom: 10px;">
                                                        <div style="background: url('__STATIC__/admin/images/44.png') no-repeat;background-size: 100% 100%;height: 30px;width: 30px;"></div>
                                                        <h5 style="margin: 0 0 0 10px;">到期数</h5>
                                                    </div>
                                                    <div style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                                                        <h2 class="no-margins daoqi" id="daoqi_click"
                                                        style="color: orange;cursor: pointer;font-size: 20px;">0</h2>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                            </div>


                        </div>

                    </div>
                </div>

            </div>

            <div class="layui-row">


                <div class="layui-col-xs6">
                    <div class="layui-card">
                        <div class="layui-card-header" style="font-size: 18px;font-weight: bold;color: #333;">欠费清单
                        </div>
                        <div class="layui-card-body">
                            <div style="background: url('__STATIC__/admin/images/top.png') no-repeat;background-size: 100% 100%;height: 60px;text-align: center;color: #fff;font-size: 20px;
    line-height: 60px;">截止目前到期总数：
                                {$qianfeiTotal|default='0'} 个
                            </div>
                            {if $qianfeiTotal > 0}
                            <table class="layui-hide" id="qianfei_table"></table>
                            {else}
                            <div style="padding: 15px;text-align: center;">暂无数据</div>
                            {/if}
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs6">

                    <div class="layui-card">
                        <div class="layui-card-header" style="font-size: 18px;font-weight: bold;color: #333;">本月到期
                        </div>
                        <div class="layui-card-body">
                            <div style="background: url('__STATIC__/admin/images/top1.png') no-repeat;background-size: 100% 100%;height: 60px;text-align: center;color: #fff;font-size: 20px;
    line-height: 60px;">本月到期总数：
                                {$benzhouTotal|default='0'} 个
                            </div>
                            {if $benzhouTotal > 0}
                            <table class="layui-hide" id="daoqi_table"></table>
                            {else}
                            <div style="padding: 15px;text-align: center;">暂无数据</div>
                            {/if}
                        </div>
                    </div>
                </div>


            </div>

            <div class="layui-row" style="margin-top: 30px;">


                <div class="layui-col-xs6">
                    <div class="layui-card">
                        <div class="layui-card-header" style="font-size: 18px;font-weight: bold;color: #333;">
                            日期筛选查询
                        </div>
                        <div class="layui-card-body">

                            <iframe frameborder="0" style="width:100%;height:500px;"
                                    scrolling="yes" src="/admin/zc.asset_order/dlist.html"></iframe>

                        </div>
                    </div>
                </div>
                <div class="layui-col-xs6">

                    <div class="layui-card">
                        <div class="layui-card-header" style="font-size: 18px;font-weight: bold;color: #333;">
                            单位筛选查询
                        </div>
                        <div class="layui-card-body">
                            <iframe frameborder="0" style="width:100%;height:500px;"
                                    scrolling="yes" src="/admin/zc.asset_order/rlist.html"></iframe>

                        </div>
                    </div>
                </div>


            </div>

            <!--<div style="margin-right: 15px;margin-top: 20px;">


                <div class="layui-card">
                    <div class="layui-card-header">
                        <div style="display: flex;align-items: center;">

                            <div>日期筛选</div>
                            <div style="margin:0 15px;">
                                <input type="text" id="date" data-date="" data-date-type="date" data-date-range="-" class="layui-input"  placeholder="请选择日期范围" value="">
                            </div>

                            <div>
                                <button type="button" id="queren" class="layui-btn layui-btn-normal layui-btn-sm">确认</button>
                                <button type="button" id="chongzhi" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">

                        <div class="layui-tab layui-tab-card">

                            <div class="layui-tab-content">

                                <div class="layui-tab-item layui-show">
                                    <div class="welcome-module">
                                        <div style="display: flex;justify-content: space-around;width: 100%;" id="type4_box">

                                            <div style="width: 23%;padding: 10px;">
                                                <div style="background-color: #F8F8F8;padding: 10px 20px;border-radius: 5px;">
                                                    <h5>应收</h5>
                                                    <div style="margin-top: 10px;">
                                                        <h1 class="no-margins yingshou">0</h1>
                                                    </div>
                                                </div>
                                            </div>

                                            <div style="width: 23%;padding: 10px;">
                                                <div style="background-color: #F8F8F8;padding: 10px 20px;border-radius: 5px;">
                                                    <h5>实收</h5>
                                                    <div style="margin-top: 10px;">
                                                        <h1 class="no-margins shishou">0</h1>
                                                    </div>
                                                </div>
                                            </div>

                                            <div style="width: 23%;padding: 10px;">
                                                <div style="background-color: #F8F8F8;padding: 10px 20px;border-radius: 5px;">
                                                    <h5>欠费总数</h5>
                                                    <div style="margin-top: 10px;">
                                                        <h1 class="no-margins qianfei">0</h1>
                                                    </div>
                                                </div>
                                            </div>

                                            <div style="width: 23%;padding: 10px;">
                                                <div style="background-color: #F8F8F8;padding: 10px 20px;border-radius: 5px;">
                                                    <h5>到期数</h5>
                                                    <div style="margin-top: 10px;">
                                                        <h1 class="no-margins daoqi">0</h1>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>


                            </div>

                        </div>

                    </div>
                </div>

            </div>-->


        </div>

    </div>


</div>
<input type="hidden" id="qianfeiData" value="{$qianfeiData}">
<input type="hidden" id="daoqiData" value="{$benzhouData}">
