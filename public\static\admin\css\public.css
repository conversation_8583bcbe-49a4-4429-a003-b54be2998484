@import url("../../plugs/layui-v2.8.x/css/layui.css");
@import url("../../plugs/font-awesome-4.7.0/css/font-awesome.min.css");
@import url("../css/iconfont.css");

html, body {
    height: 100%;
    background: #f2f2f2;
}

html.dark, body {
    height: 100%;
    background: var(--lay-color-bg-1);
}

.ws-header-theme .layui-form-switch {
    vertical-align: baseline;
}

.layuimini-container {
    min-height: 250px;
    padding: 15px;
    margin: 0 auto;
}

.layuimini-main {
    position: relative;
    padding: 15px 15px;
    background-color: #ffffff;
    border: 1px solid #f2f2f2;
    border-radius: 5px;
}

.dark .layuimini-main {
    position: relative;
    padding: 15px 15px;
    background-color: var(--lay-color-bg-1);
    border: 1px solid #363636;
    border-radius: 5px;
}

.layuimini-form .layui-form-item {
    position: relative;
    padding: 0 60px 0 0;
    line-height: 24px;
}

.text-center {
    text-align: center;
}

.layuimini-form {
    margin-top: 10px;
}

.easy-bg-white {
    background-color: #ffffff;
    height: auto;
}

.hr-line {
    color: #fff;
    height: 1px;
    margin: 30px 0;
    background-color: #fff;
    border-top: 1px dashed #e7eaec;
}

.dark .hr-line {
    color: #363636;
    height: 1px;
    margin: 30px 0;
    background-color: #363636;
    border-top: 1px dashed #363636;
}

/**重写layui表格自适应*/
.layuimini-container .layui-table-cell {
    height: 100%;
    max-width: 100%;
}

/**数据表格-搜索表单样式*/
.layuimini-container .table-search-fieldset {
    margin: 0;
    border: 1px solid #e6e6e6;
    padding: 10px 20px 5px 20px;
    color: #6b6b6b;
}

/**数据表格-搜索表单样式*/
.dark .layuimini-container .table-search-fieldset {
    margin: 0;
    border: 1px solid #363636;
    padding: 10px 20px 5px 20px;
    color: #bdbdbd;
}

.layuimini-container .table-search-fieldset input::-webkit-input-placeholder {
    color: #a9a9a9;
}

.layuimini-container .table-search-fieldset input:-ms-input-placeholder {
    color: #a9a9a9;
}

.layuimini-container .table-search-fieldset input::-ms-input-placeholder {
    color: #a9a9a9;
}

/**图标选择器*/
.layui-iconpicker-body.layui-iconpicker-body-page .hide {
    display: none;
}

/**必填红点 */
.required:after {
    content: '*';
    color: red;
    position: absolute;
    margin-left: 4px;
    font-weight: bold;
    line-height: 1.8em;
    top: 6px;
    right: 5px;
}

/*.layuimini-form>.layui-form-item>.layui-form-label {width:120px !important;}*/
/*.layuimini-form>.layui-form-item>.layui-input-block {margin-left:150px !important;}*/
.layuimini-form > .layui-form-item > .layui-input-block tip, .layuimini-form > .layui-form-item > .layui-inline tip {
    display: inline-block;
    margin-top: 10px;
    line-height: 15px;
    font-size: 14px;
    color: #ff7d00;
}

/** 按钮背景色 */
.layuimini-container .layuimini-btn-primary {
    color: #fff;
    background-color: #2c3e50;
}

.layuimini-container .layui-btn-sm i {
    font-size: 12px !important;
}

/**文件上传样式*/
.layuimini-upload {
    position: relative;
}

.layuimini-upload .layuimini-upload-btn {
    display: inline-block;
    position: absolute;
    right: 0px;
    background-color: #fff;
}

.dark .layuimini-upload .layuimini-upload-btn {
    display: inline-block;
    position: absolute;
    right: 0px;
    background-color: #363636;
}

.layuimini-upload-show {
    margin-top: 10px;
    margin-bottom: 0;
}

.layuimini-upload-show li {
    position: relative;
    display: inline-block;
    padding: 5px 0 5px 0;
    padding-left: 10px;
    padding-right: 10px;
    border: 1px solid #e2e2e2;
}

.layuimini-upload-show a img {
    height: 80px;
    object-fit: cover;
}

.layuimini-upload-show .uploads-delete-tip {
    position: absolute;
    right: 10px;
    font-size: 12px;
}

.bg-red {
    background-color: #e74c3c !important;
}

.color-red {
    color: #e74c3c !important;
}

.badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 11px;
    font-weight: bold;
    color: #fff;
    line-height: 1;
    vertical-align: middle;
    white-space: nowrap;
    text-align: center;
    background-color: #777777;
    border-radius: 10px;
}

/**vue隐藏样式*/
[v-cloak] {
    display: none;
}

/**表格url样式*/
.layuimini-table-url {
    color: #1497f3;
    border-radius: 3px;
    size: 8px !important;
    padding: 2px
}

.layuimini-table-url:hover {
    color: #1497f3;
    border-radius: 3px;
    size: 8px !important;
    padding: 2px
}

/**后台权限隐藏*/
/*[auth] { display: none; }*/

.layui-form-label {
    width: 100px;
}

.layui-input-block {
    margin-left: 130px;
    min-height: 36px
}

/**
table样式
 */
.layuimini-container .layui-laypage .layui-laypage-curr .layui-laypage-em {
    border-radius: 30px !important;
    background-color: #165dff;
}

.layuimini-container .layui-table-tool {
    background-color: #ffffff;
    border-bottom: none !important;
    padding-bottom: 15px !important;
}

.dark .layuimini-container .layui-table-tool {
    background-color: var(--lay-color-bg-1);
    border-bottom: none !important;
    padding-bottom: 15px !important;
}

.layuimini-container .layui-table-view {
    border: none !important;
}

.layuimini-container .layui-table-box {
    border-width: 1px;
    border-style: solid;
    border-color: #e6e6e6;
}

.dark .layuimini-container .layui-table-box {
    border-width: 1px;
    border-style: solid;
    border-color: #363636;
}

.layuimini-container .layui-table-page, .layui-table-total {
    border-width: 0px 0 0;
}

.layuimini-container .layui-table-box .layui-table-header th {
    font-weight: bold !important;
    color: #565656 !important;
}

/**
搜索
 */
.form-search .layui-btn {
    height: 32px;
    line-height: 28px;
    font-size: 12px;
    padding: 0 10px;
}

.form-search .layui-form-label {
    padding: 0 8px;
    height: 32px;
    line-height: 30px;
}

.form-search .layui-input-inline {
    width: 170px;
}

.form-search .layui-input-inline input,
.form-search .layui-input-inline select {
    width: 100%;
    height: 32px;
    padding: 2px 8px;
    line-height: 1em;
}

.form-search .layui-form-select dl {
    top: 31px;
    padding: 0;
}


/**
按钮
 */
/*.layuimini-container .layui-btn-success {*/
/*    color: #fff;*/
/*    background-color: #4bb368;*/
/*    border-color: #4bb368;*/
/*}*/

/*.layuimini-container .layui-btn-danger {*/
/*    color: #fff;*/
/*    background-color: #f56c6c;*/
/*    border-color: #f56c6c;*/
/*}*/

.layuimini-container .layui-table-tool .layui-btn + .layui-btn {
    margin-left: 5px;
}

.layuimini-container .layui-table-tool .layui-inline[lay-event] {
    width: 30px;
    height: 30px;
    line-height: 30px;
    padding: 0px;
}

.layuimini-container .layui-table-tool .layui-inline .layui-icon {
    font-size: 16px;
}

.layuimini-container .layui-table-tool-self {
    right: 0px;
}

.layuimini-container .layui-table-tool {
    padding: 10px 0px;
}

/**
弹出层样式
 */
.layui-layer-easy .layui-layer-title {
    background: #2c3e50 !important;
    color: #fff !important;
    border-bottom: none;
}

.layui-layer-easy.layui-layer-border {
    border: none !important;
    box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3) !important;
}

.layui-layer-easy.layui-layer-iframe {
    overflow: visible;
}

.layui-layer-easy .layui-layer-moves {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}

.layui-layer-easy .layui-layer-btn {
    text-align: center !important;
    padding: 10px !important;
    background: #ecf0f1;
    overflow: hidden;
}

.layui-layer-easy .layui-layer-btn a {
    background-color: #95a5a6;
    color: #fff !important;
    height: 31px;
    margin-top: 0;
    border: 1px solid #95a5a6;
}

.layui-layer-easy .layui-layer-btn .layui-layer-btn0 {
    background-color: #1E9FFF;
    border-color: #1E9FFF;
}

.layui-layer-easy .layui-layer-footer {
    padding: 8px 20px;
    background-color: #ecf0f1;
    height: auto;
    text-align: inherit !important;
}

.layui-layer-easy .layui-layer-setwin > a {
    background: none !important;
}

.layui-layer-easy .layui-layer-setwin > a cite {
    display: none;
}

.layui-layer-easy .layui-layer-setwin > a:after {
    content: "\e625";
    font-family: iconfont;
    font-style: normal;
    font-weight: normal;
    text-decoration: inherit;
    position: absolute;
    font-size: 18px;
    color: #fff;
    margin: 0;
    z-index: 1;
}

.layui-layer-easy .layui-layer-setwin > a:hover {
    text-decoration: none !important;
    background: none !important;
}

.layui-layer-easy .layui-layer-setwin > a:focus {
    text-decoration: none !important;
}

.layui-layer-easy .layui-layer-setwin .layui-layer-min:before,
.layui-layer-easy .layui-layer-setwin .layui-layer-min:after {
    border-color: #FFFFFF;
}

.layui-layer-easy .layui-layer-setwin .layui-layer-max:before,
.layui-layer-easy .layui-layer-setwin .layui-layer-max:after {
    border-color: #FFFFFF;
}


.layui-layer-easy .layui-layer-setwin .layui-layer-close1,
.layui-layer-easy .layui-layer-setwin .layui-layer-close1:hover {
    color: #FFFFFF;
}

.layui-layer-easy .layui-layer-setwin .layui-layer-close1:after,
.layui-layer-easy .layui-layer-setwin .layui-layer-close1:hover:after {
    display: none;
}

.layui-layer-easy .layui-layer-setwin .layui-layer-close2,
.layui-layer-easy .layui-layer-setwin .layui-layer-close2:hover {
    color: #FFFFFF;
}

.layui-layer-easy .layui-layer-setwin .layui-layer-close2:after,
.layui-layer-easy .layui-layer-setwin .layui-layer-close2:hover:after {
    display: none;
}

.layui-layer-content {
    clear: both;
}

.layui-layer-easy-msg {
    min-width: 100px;
}

.layui-layer-easy-tab .layui-layer-title .layui-this {
    color: #333;
}

.layui-layer-easy-tab .layui-layer-content .layui-layer-tabmain {
    margin: 0;
    padding: 0;
}

@media screen and (max-width: 1024px) {
    .layuimini-form .layui-form-item {
        position: relative;
        padding: 0 30px 0 0;
        line-height: 24px;
    }
}

@media screen and (max-width: 768px) {
    .easyadmin-export-btn {
        display: none;
    }
}

/**
资源加载锁
 */
.easy-load-lock {
    cursor: not-allowed;
}

.line-limit-length {
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    background: goldenrod;
    white-space: nowrap;
}


.layui-table tbody tr:hover, .layui-table thead tr, .layui-table-click, .layui-table-header, .layui-table-hover, .layui-table-mend, .layui-table-patch, .layui-table-tool, .layui-table-total, .layui-table-total tr, .layui-table[lay-even] tr:nth-child(even) {
    background-color: #e0e0e0;
}

.titles {
    padding: 10px;
    background: #444c69;
    margin-bottom: 10px;
    color: #fff;
    border-radius: 6px;
}
.titles:before {
    content: '';
    border: 2px solid #fff;
    margin-right: 10px;
}
.titles .desc {
    font-size: 12px;
    margin-left: 10px;
    color: #ccc;
}

.layuimini-container .layui-btn-primary {
    color: #4e5969;
    background-color: #f2f3f5;
    border-color: #f2f3f5;
}

.layuimini-container .layui-btn-normal {
    color: #fff;
    background-color: #165dff;
    border-color: #165dff;
}

.layuimini-container .layui-btn-success {
    color: #fff;
    background-color: #16baaa;
    border-color: #16baaa;
}

.layuimini-container .layui-btn-info {
    color: #fff;
    background-color: #909399;
    border-color: #909399;
}

.layuimini-container .layui-btn-warm {
    color: #fff;
    background-color: #ff7d00;
    border-color: #ff7d00;
}

.layuimini-container .layui-btn-danger {
    color: #fff;
    background-color: #f53f3f;
    border-color: #f53f3f;
}

.layui-table-view .layui-form-checkbox, .layui-table-view .layui-form-radio, .layui-table-view .layui-form-switch{
    vertical-align: auto;
}
/*radio样式*/
.layui-form-switch{
    display: inline-flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 5px 0 8px;
}
.layui-form{
    background: #fff;
    padding: 20px 0 10px;
    border-radius: 5px;
}

.layuimini-form input:disabled, .layuimini-form input:read-only {
    background-color: #ebebeb;
    color: #666;
    opacity: 1;
    -webkit-text-fill-color: #666;
    -webkit-opacity: 1;
}

.layuimini-upload-show {
    margin-top: 10px;
    margin-bottom: 0;
}

.layuimini-upload-show li {
    position: relative;
    display: inline-block;
    padding: 5px 10px;
    border: 1px solid #e2e2e2;
}

.layuimini-upload-show li:not(:last-child) {
    margin-right: 10px;
}

.layuimini-upload-show li img {
    height: 80px;
    object-fit: cover;
}

.layuimini-upload-show .uploads-delete-tip {
    position: absolute;
    right: 10px;
    font-size: 12px;
    cursor: pointer;
}
