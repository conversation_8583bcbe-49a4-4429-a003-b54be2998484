<?php

namespace app\admin\controller\zc;

use app\admin\model\ZcAssetOrder;
use app\common\controller\AdminController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use EasyAdmin\tool\CommonTool;
use jianyan\excel\Excel;
use think\App;
use think\facade\Db;
use zjkal\TimeHelper;

/**
 * @ControllerAnnotation(title="台账续费表")
 */
class AssetOrderLog extends AdminController
{
	
	use \app\admin\traits\Curd;
	
	public function __construct(App $app)
	{
		parent::__construct($app);
		
		$this->model = new \app\admin\model\ZcAssetOrderLog();
		
	}
	
	
	/**
	 * @NodeAnotation(title="列表")
	 */
	public function index()
	{
		if ($this->request->isAjax()) {
			if (input('selectFields')) {
				return $this->selectList();
			}
			list($page, $limit, $where) = $this->buildTableParames();
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[] = [
					'zc_asset_order_log.create_by',
					'in',
					$dataScope
				];
			}
			$count = $this->model->withJoin([
				'logs',
				'creator',
				'asset'
			], 'LEFT')
			                     ->where($where)
			                     ->count();
			$list  = $this->model->withJoin([
				'logs',
				'creator',
				'asset'
			], 'LEFT')
			                     ->where($where)
			                     ->page($page, $limit)
			                     ->order($this->sort)
			                     ->select();
			$data  = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="财务收款")
	 */
	public function list()
	{
		$oid = input('oid/d', 0);
		if ($this->request->isAjax()) {
			list($page, $limit, $where) = $this->buildTableParames();
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[] = [
					'zc_asset_order_log.create_by',
					'in',
					$dataScope
				];
			}
			$where[] = [
				'order_id',
				'=',
				$oid
			];
			$count   = $this->model->withJoin([
				'logs',
				'creator',
				'asset'
			], 'LEFT')
			                       ->where($where)
			                       ->count();
			$list    = $this->model->withJoin([
				'logs',
				'creator',
				'asset'
			], 'LEFT')
			                       ->where($where)
			                       ->page($page, $limit)
			                       ->order($this->sort)
			                       ->select();
			$data    = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		$this->assign('oid', $oid);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="添加")
	 */
	public function add()
	{
		$oid     = input('oid/d', 0);
		$oidInfo = ZcAssetOrder::with(['asset'])
		                       ->where('id', $oid)
		                       ->findOrEmpty();
		if ($oidInfo->isEmpty()) {
			$this->error('台账不存在');
		}
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			$leiji = $oidInfo->getData('leiji');
			$price = $post['price'];
			// 验证价格
			if ($price <= 0) {
				$this->error('收款金额必须大于0');
			}
			
			try {
				$endTime      = strtotime($post['end_date']);
				$oidStartTime = $oidInfo->getData('start_date');
				if ($endTime <= $oidStartTime) {
					$this->error('结租时间不能小于等于起始时间');
				}
				$oidEndTime = $oidInfo->getData('end_date');
				if ($endTime > $oidEndTime) {
					$this->error('结租时间不能大于等于到期时间');
				}
				$data = [
					'last_time' => strtotime($post['end_date']),
				];
				if ($leiji > 0) {
					if ($price < $leiji) {
						$this->error('收款金额不能小于总欠费金额');
					}
					$data['qianfei']  = 0;
					$data['zhinajin'] = 0;
					$data['leiji']    = 0;
				}
				$oidInfo->save($data);
				$post['asset_id'] = $oidInfo->getData('asset_id');
				$save             = $this->model->saveFromCreateId($post);
			}
			catch (\Exception $e) {
				$this->error('保存失败:' . $e->getMessage());
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('oidInfo', $oidInfo);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="编辑")
	 * @param $id
	 * @return
	 */
	/*public function edit($id)
	{
		$where     = [];
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->with([
			'asset',
			'logs',
			'logs.zulin'
		])
		                   ->where($where)
		                   ->find($id);
		empty($row) && $this->error('数据不存在');
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$orderInfo = $row->logs;
				$endTime   = strtotime($post['end_date']);
				if ($orderInfo->last_time != $endTime) {
					$oidStartTime = $orderInfo->getData('start_date');
					if ($endTime <= $oidStartTime) {
						$this->error('结租时间不能小于等于起始时间');
					}
					$oidEndTime = $orderInfo->getData('end_date');
					if ($endTime > $oidEndTime) {
						$this->error('结租时间不能大于等于到期时间');
					}
					$row->logs->save([
						'last_time' => $endTime,
					]);
				}
				$save = $row->save($post);
			}
			catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('row', $row);
		return $this->fetch();
	}*/
	
	/**
	 * @NodeAnotation(title="删除")
	 */
	/*public function delete($id)
	{
		$where     = [];
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->whereIn('id', $id)
		                   ->where($where)
		                   ->select();
		$row->isEmpty() && $this->error('数据不存在');
		try {
			$save = $row->delete();
		}
		catch (\Exception $e) {
			$this->error('删除失败');
		}
		$save
			? $this->success('删除成功')
			: $this->error('删除失败');
	}*/
	
	/**
	 * @NodeAnotation(title="导出")
	 * @param $id
	 * @return
	 */
	public function export()
	{
		list($page, $limit, $where) = $this->buildTableParames();
		$header = [
			[
				'租赁名称',
				'asset_title'
			],
			[
				'合同编号',
				'contract_no'
			],
			[
				'面积',
				'area'
			],
			[
				'租房人单位名称',
				'zulin'
			],
			[
				'起租日期',
				'start_date'
			],
			[
				'结租日期',
				'end_date'
			],
			[
				'租期/天',
				'total_day'
			],
			[
				'剩余天数',
				'shengyu_day'
			],
			[
				'合同金额',
				'hetong_price'
			],
			[
				'缴费日期',
				'jiezu_date'
			],
			[
				'凭证号',
				'pay_no'
			],
			[
				'押金',
				'ya_price'
			],
			[
				'实收房租',
				'price'
			],
			[
				'增值税',
				'zzs'
			],
			[
				'小计',
				'xj'
			],
			[
				'房产税',
				'fcs'
			],
			[
				'城建税',
				'cjs'
			],
			[
				'教育费附加',
				'jyfj'
			],
			[
				'地方教育附加',
				'dfjyfj'
			],
			[
				'水利基金',
				'sljj'
			],
			[
				'电子发票',
				'dzfp'
			],
			[
				'欠租金额',
				'leiji'
			],
		];
		$list   = $this->model->withJoin([
			'logs',
			'creator',
			'asset'
		], 'LEFT')
		                      ->where($where)
		                      ->limit(100000)
		                      ->order('id', 'desc')
		                      ->order($this->sort)
		                      ->select();
		$data   = [];
		foreach ($list as $item) {
			$totalDay = TimeHelper::diffDays($item->logs->end_date, $item->logs->start_date);
			$nowDay   = TimeHelper::diffDays($item->logs->end_date, time());
			$data[]   = [
				'asset_title'  => $item->logs->asset->title,
				'contract_no'  => $item->logs->contract_no,
				'area'         => $item->logs->area,
				'zulin'        => $item->logs->zulin->title,
				'start_date'   => $item->logs->start_date,
				'end_date'     => $item->logs->end_date,
				'total_day'    => $totalDay,
				'shengyu_day'  => $nowDay,
				'hetong_price' => $item->logs->total,
				'jiezu_date'   => $item->end_date,
				'pay_no'       => $item->pay_no,
				'ya_price'     => $item->logs->ya_price,
				'price'        => $item->price,
				'zzs'          => 0,
				'fcs'          => 0,
				'cjs'          => 0,
				'jyfj'         => 0,
				'dfjyfj'       => 0,
				'sljj'         => 0,
				'dzfp'         => 0,
				'leiji'        => $item->logs->leiji,
			];
		}
		$fileName = '收款记录' . date('Y-m-d');
		return Excel::exportData($data, $header, $fileName, 'xlsx');
	}
}