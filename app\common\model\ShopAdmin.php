<?php

namespace app\common\model;

use app\shop\model\ShopAuth;
use app\shop\model\SystemDept;
use app\shop\model\SystemJob;
use app\shop\traits\ModelTrait;

class ShopAdmin extends TimeModel
{
	
	use ModelTrait;
	
	
	protected $name = "shop_admin";
	
	protected $deleteTime = "delete_time";
	
	public function shop()
	{
		return $this->belongsTo(Shop::class, 'shop_id');
	}
	
	public function dept()
	{
		return $this->belongsTo(SystemDept::class, 'dept_id');
	}
	
	public function job()
	{
		return $this->belongsTo(SystemJob::class, 'job_id');
	}
	
	public static function getAdminInfo($id,$shopId)
	{
		return self::where('shop_id', $shopId)->where('status', 1)
		           ->findOrEmpty($id);
	}
	
	public static function getAdminInfoByShopId($id)
	{
		return self::where('id', $id)
		           ->where('shop_id', session('shop.shop_id'))
		           ->where('status', 1)
		           ->findOrEmpty();
	}
	
	public static function shopAdminList()
	{
		$map = [
			[
				'shop_id',
				'=',
				session('shop.shop_id')
			],
			[
				'status',
				'=',
				1
			],
		];
		return self::where($map)
		           ->field('username')
		           ->select();
	}
	
	public function getAuthList($shopId = 0)
	{
		return (new ShopAuth())->where('shop_id', $shopId)
		                       ->where('status', 1)
		                       ->column('title', 'id');
	}
	
}