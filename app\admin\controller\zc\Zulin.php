<?php

namespace app\admin\controller\zc;

use app\common\controller\AdminController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use think\App;

/**
 * @ControllerAnnotation(title="租赁单位")
 */
class Zulin extends AdminController
{

    use \app\admin\traits\Curd;

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->model = new \app\admin\model\ZcZulin();
        
    }
	
	/**
	 * @NodeAnotation(title="列表")
	 */
	public function index()
	{
		if ($this->request->isAjax()) {
			if (input('selectFields')) {
				return $this->selectList();
			}
			list($page, $limit, $where) = $this->buildTableParames();
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[]   = [
					'zc_zulin.create_by',
					'in',
					$dataScope
				];
			}
			$count = $this->model->withJoin(['creator'], 'LEFT')
				->where($where)
				->count();
			$list = $this->model->with<PERSON>oin(['creator'], 'LEFT')
				->where($where)
				->page($page, $limit)
				->order($this->sort)
				->select();
			$data = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="添加")
	 */
	public function add()
	{
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $this->model->saveFromCreateId($post);
			} catch (\Exception $e) {
				$this->error('保存失败:'.$e->getMessage());
			}
			$save ? $this->success('保存成功') : $this->error('保存失败');
		}
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="编辑")
	 * @param $id
	 * @return
	 */
	public function edit($id)
	{
		$where = [];
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->where($where)->find($id);
		empty($row) && $this->error('数据不存在');
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $row->save($post);
			} catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save ? $this->success('保存成功') : $this->error('保存失败');
		}
		$this->assign('row', $row);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="删除")
	 */
	public function delete($id)
	{
		$where = [];
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[] = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->whereIn('id', $id)->where($where)->select();
		$row->isEmpty() && $this->error('数据不存在');
		try {
			$save = $row->delete();
		} catch (\Exception $e) {
			$this->error('删除失败');
		}
		$save ? $this->success('删除成功') : $this->error('删除失败');
	}

    
}