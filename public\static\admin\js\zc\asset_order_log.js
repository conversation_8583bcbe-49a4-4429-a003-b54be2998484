define(["jquery", "easy-admin"], function ($, ea) {


    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'zc.asset_order_log/index',
        add_url: 'zc.asset_order_log/add',
        edit_url: 'zc.asset_order_log/edit',
        delete_url: 'zc.asset_order_log/delete',
        export_url: 'zc.asset_order_log/export',
        modify_url: 'zc.asset_order_log/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh','export'],
                cols: [[
                    {type: 'checkbox'},
                    {type: 'numbers', title: '序号'},
                    {field: 'asset.title', title: '资产名称'},
                    {field: 'logs.contract_no', title: '合同编号'},
                    {field: 'price', title: '金额', search: false},
                    {field: 'invoice_image', title: '发票图片', templet: ea.table.image, search: false},
                    {field: 'pay_image', title: '收款依据', templet: ea.table.image, search: false},
                    {field: 'pay_no', title: '付款凭证号'},
                    {field: 'end_date', title: '结租日期', search: 'date'},
                    {field: 'creator_name', title: '创建人', search: false},
                    {field: 'create_time', title: '创建时间', search: 'date_range'},
                    // {width: 150, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        list: function () {
            var oid = $("#oid").val() || 0;
            ea.table.render({
                url: ea.url('zc.asset_order_log/list?oid=' + oid),
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url + '&oid=' + oid,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],],
                cols: [[
                    {type: 'checkbox'},
                    {type: 'numbers', title: '序号'},
                    {field: 'asset.title', title: '资产名称'},
                    {field: 'logs.contract_no', title: '合同编号'},
                    {field: 'price', title: '金额', search: false},
                    {field: 'invoice_image', title: '发票图片', templet: ea.table.image, search: false},
                    {field: 'pay_image', title: '收款依据', templet: ea.table.image, search: false},
                    {field: 'pay_no', title: '付款凭证号'},
                    {field: 'end_date', title: '结租日期', search: 'date'},
                    {field: 'creator_name', title: '创建人', search: false},
                    {field: 'create_time', title: '创建时间', search: 'date_range'},
                    {width: 150, title: '操作', templet: ea.table.tool,operat: [
                            [{
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url + '&oid=' + oid,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            },],]},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});