<?php
namespace app\common\service;

use app\common\constants\MenuConstant;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\Model;

abstract class MenuService
{

    protected $tableName = '';

    /**
     * 管理员ID
     * @var integer
     */
    protected $adminId;

    public function __construct($adminId)
    {
        $this->adminId = $adminId;
        return $this;
    }

    /**
     * 获取首页信息
     * @return array|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getHomeInfo()
    {
        $data = Db::name($this->tableName)
            ->field('title,icon,href')
            ->whereNull("delete_time")
            ->where('pid', MenuConstant::HOME_PID)
            ->find();
        !empty($data) && $data['href'] = __url($data['href']);
        return $data;
    }

    /**
     * 获取后台菜单树信息
     * @return mixed
     */
    abstract public function getMenuTree();

    protected function buildMenuChild($pid, $menuList, CommonAuthService $authServer,$type = 0)
    {
        $treeList = [];
        foreach ($menuList as &$v) {
            $check = empty($v['href']) ? true : $authServer->checkNode($v['href']);
            !empty($v['href']) && $type === 0 && $v['href'] = __url($v['href']);
            if ($pid == $v['pid'] && $check) {
                $node = $v;
                $child = $this->buildMenuChild($v['id'], $menuList, $authServer);
                if (!empty($child)) {
                    $node['child'] = $child;
                }
                if (!empty($v['href']) || !empty($child)) {
                    $treeList[] = $node;
                }
            }
        }
        return $treeList;
    }

    /**
     * 获取所有菜单数据
     * @return \think\Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getMenuData()
    {
        return Db::name($this->tableName)
            ->field('id,pid,title,icon,href,target')
            ->where([
                ['status', '=', 1],
                ['pid', '<>', MenuConstant::HOME_PID],
            ])
            ->whereNull("delete_time")
            ->order([
                'sort' => 'desc',
                'id'   => 'asc',
            ])
            ->select();
    }
}
