<?php



namespace app\admin\model;


use app\admin\traits\ModelTrait;
use app\common\model\TimeModel;

class SystemAdmin extends TimeModel
{
	
	use ModelTrait;

    protected $deleteTime = 'delete_time';
	
	public function dept()
	{
		return $this->belongsTo(SystemDept::class, 'dept_id');
	}

    public function getAuthList()
    {
	    $adminInfo = session('admin');
	    $where = [];
	    $dataScope = $this->getThisModelDataScope();
	    if ($dataScope !== true) {
		    $where[]   = [
			    'create_by',
			    'in',
			    $dataScope
		    ];
	    }
        $list = (new SystemAuth())->where($where)
            ->where('status', 1)
            ->column('title', 'id');
        return $list;
    }
	
	public static function getAdminInfoByShopId($id)
	{
		return self::where('id', $id)
		           ->where('status', 1)
		           ->findOrEmpty();
	}

}