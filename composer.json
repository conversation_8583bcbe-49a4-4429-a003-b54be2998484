{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0", "topthink/think-orm": "^2.0", "topthink/think-multi-app": "^1.0", "topthink/think-view": "^1.0", "doctrine/annotations": "^1.8", "topthink/think-captcha": "^3.0", "aliyuncs/oss-sdk-php": "^2.3", "qcloud/cos-sdk-v5": "^2.0", "qiniu/php-sdk": "^7.2", "alibabacloud/client": "^1.5", "jianyan74/php-excel": "^1.0", "alibabacloud/dysmsapi-20170525": "^1.0", "topthink/think-trace": "^1.4", "overtrue/wechat": "~4.0", "zhongshaofa/easy-admin": "^1.0", "hectorqin/think-wechat": "^1.0", "ext-json": "*", "ext-curl": "*", "ext-bcmath": "*", "kkokk/poster": "^2.3", "intervention/image": "^2.7", "ext-gd": "*", "zjkal/time-helper": "^1.1", "workerman/workerman": "^4.1"}, "require-dev": {"symfony/var-dumper": "^4.2", "eaglewu/swoole-ide-helper": "dev-master"}, "autoload": {"psr-4": {"app\\": "app", "addons\\": "addons", "EasyAdmin\\": "vendor/zhongshaofa/easy-admin/src", "ServiceSwoole\\": "vendor/zhongshaofa/service-swoole/src"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "secure-http": false, "optimize-autoloader": true, "allow-plugins": {"easywechat-composer/easywechat-composer": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "repositories": {"packagist": {"type": "composer", "url": "https://mirrors.aliyun.com/composer/"}}}