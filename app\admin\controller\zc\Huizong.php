<?php

namespace app\admin\controller\zc;

use app\admin\model\ZcAssetOrder;
use app\common\controller\AdminController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use think\App;

/**
 * @ControllerAnnotation(title="汇总信息")
 */
class Huizong extends AdminController
{
	
	public function __construct(App $app)
	{
		parent::__construct($app);
		
		$this->model = new \app\admin\model\ZcAssetOrder();
		
	}
	
	/**
	 * @NodeAnotation(title="列表")
	 */
	public function index()
	{
		if ($this->request->isAjax()) {
			$type     = input('type/d', 0);
			$year     = input('date', '');
			$calcData = ZcAssetOrder::calcOrder($type, $year);
			$data     = [
				'code'  => 0,
				'msg'   => '',
				'count' => 0,
				'data'  => $calcData,
			];
			return json($data);
		}
		$qianfeiData = ZcAssetOrder::with([
			'asset',
			'zulin',
			'dept'
		])
		                           ->where('leiji', '>', 0)
		                           ->select();
		foreach ($qianfeiData as $key => $value) {
			$qianfeiData[$key]['asset_title'] = $value['asset']['title'];
			$qianfeiData[$key]['zulin_title'] = $value['zulin']['title'];
			$qianfeiData[$key]['dept_title']  = $value['dept']['name'];
		}
		$this->assign('qianfeiData', json_encode($qianfeiData));
		$this->assign('qianfeiTotal', count($qianfeiData));
		
		$benzhouData = ZcAssetOrder::with([
			'asset',
			'zulin',
			'dept'
		])
//		                           ->where('qianfei', '>', 0)
		                           ->whereMonth('end_date')
		                           ->select();
		foreach ($benzhouData as $key => $value) {
			$benzhouData[$key]['asset_title'] = $value['asset']['title'];
			$benzhouData[$key]['zulin_title'] = $value['zulin']['title'];
			$benzhouData[$key]['dept_title']  = $value['dept']['name'];
		}
		$this->assign('benzhouData', json_encode($benzhouData));
		$this->assign('benzhouTotal', count($benzhouData));
		return $this->fetch();
	}
	
	
}