<?php
declare (strict_types=1);

namespace app\command;

use app\admin\model\ZcAssetOrder;
use app\admin\model\ZcTask;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Log;
use Workerman\Lib\Timer as TimerLib;
use Workerman\Worker;

class Timer extends Command
{
	/**
	 * @var int
	 */
	protected $timer = [];
	/**
	 * @var int|float
	 */
	protected $interval = 10;
	
	protected function configure()
	{
		// 指令配置
		$this->setName('timer')
		     ->addArgument('status', Argument::REQUIRED, 'start/stop/reload/status/connections')
		     ->addOption('d', null, Option::VALUE_NONE, 'daemon（守护进程）方式启动')
		     ->addOption('i', null, Option::VALUE_OPTIONAL, '多长时间执行一次')
		     ->setDescription('开启/关闭/重启 定时任务');
	}
	
	protected function init(Input $input, Output $output)
	{
		global $argv;
		if ($input->hasOption('i')) {
			$this->interval = floatval($input->getOption('i'));
		}
		$argv[1] = $input->getArgument('status')
			?: 'start';
		if ($input->hasOption('d')) {
			$argv[2] = '-d';
		}
		else {
			unset($argv[2]);
		}
	}
	
	protected function execute(Input $input, Output $output)
	{
		$this->init($input, $output);
		//创建定时器任务
		$task                = new Worker();
		$task->count         = 1;
		$task->onWorkerStart = [
			$this,
			'start'
		];
		$task->runAll();
	}
	
	public function stop()
	{
		//手动暂停定时器
		foreach ($this->timer as $item) {
			TimerLib::del($item);
		}
	}
	
	public function start()
	{
		$this->timer[] = TimerLib::add($this->interval, function () {
			
			$today  = ZcTask::whereDay('create_time')
			                      ->findOrEmpty();
			if ($today->isEmpty()) {
				$count = 0;
				$list = ZcAssetOrder::whereTime('end_date','>=', date('Y-m-d'))->select();
				foreach ($list as $item) {
					try {
						ZcAssetOrder::calcQianfei($item);
						$count++;
					}
					catch (\Exception $e) {
						Log::record('错误信息:' . $e->getMessage());
						continue;
					}
				}
				echo '处理成功:' . $count . '条数据';
				echo PHP_EOL;
			}
			
			// todo 年租金计算任务
			
		});
		
	}
}
