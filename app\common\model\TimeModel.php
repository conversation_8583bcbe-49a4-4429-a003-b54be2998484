<?php

namespace app\common\model;

use app\admin\model\SystemAuth;
use think\Model;
use think\model\concern\SoftDelete;

/**
 * 有关时间的模型
 * Class TimeModel
 *
 * @package app\common\model
 */
class TimeModel extends Model
{
	use SoftDelete;
	
	/**
	 * 自动时间戳类型
	 *
	 * @var string
	 */
	protected $autoWriteTimestamp = true;
	
	/**
	 * 添加时间
	 *
	 * @var string
	 */
	protected $createTime = 'create_time';
	
	/**
	 * 更新时间
	 *
	 * @var string
	 */
	protected $updateTime = 'update_time';
	
	/**
	 * 软删除
	 */
	protected $deleteTime = false;
	
	
	// type 0:无关联 1 关联查询
	public function getThisModelDataScope()
	{
		try {
			return (new SystemAuth())->getUserDataScopeList();
		}
		catch (\Exception $e) {
			return [];
		}
	}
	
}