---
description: 
globs: 
alwaysApply: false
---
# 表格组件使用指南

表格组件是后台管理系统最核心的功能，由 [easy-admin.js](mdc:public/static/plugs/easy-admin/easy-admin.js) 中的 `admin.table` 模块提供，基于LayUI table进行扩展。

## 基础用法

```javascript
admin.table.render({
    elem: '#currentTable',
    url: '{:url("index")}',
    cols: [[
        {type: "checkbox"},
        {field: 'id', title: 'ID', sort: true},
        {field: 'title', title: '标题', minWidth: 100},
        {field: 'status', title: '状态', templet: admin.table.switch},
        {field: 'create_time', title: '创建时间', search: 'range'},
        {width: 250, title: '操作', templet: admin.table.tool}
    ]],
    page: true,
    search: true,
    toolbar: ['refresh','add','delete','export']
});
```

## 常用表格列模板

1. **开关组件**:
```javascript
{field: 'status', title: '状态', templet: admin.table.switch}
```

2. **图片显示**:
```javascript
{field: 'image', title: '图片', templet: admin.table.image}
```

3. **操作按钮**:
```javascript
{width: 250, title: '操作', templet: admin.table.tool}
```

4. **自定义下拉**:
```javascript
{field: 'gender', title: '性别', selectList: {1: '男', 2: '女'}}
```

## 搜索功能

表格的搜索功能会根据列定义自动生成搜索表单，支持多种搜索类型：

```javascript
// 文本搜索
{field: 'title', title: '标题', search: true}

// 下拉选择
{field: 'status', title: '状态', search: 'select', selectList: {1: '启用', 0: '禁用'}}

// 日期范围
{field: 'create_time', title: '创建时间', search: 'range'}

// 日期选择
{field: 'start_date', title: '开始日期', search: 'date'}
```

## 工具栏按钮

表格顶部工具栏按钮配置：

```javascript
toolbar: ['refresh','add','delete','export']

// 或自定义按钮
toolbar: ['refresh','add','delete', {
    title: '批量操作',
    auth: 'batch',
    class: 'layui-btn layui-btn-warm',
    icon: 'fa fa-cogs',
    url: 'batch',
    method: 'request'
}]
```

## 行操作按钮

表格行操作按钮默认有编辑和删除，也可自定义：

```javascript
{width: 250, title: '操作', templet: admin.table.tool, operat: [
    'edit', 'delete', 
    {
        class: 'layui-btn layui-btn-xs layui-btn-warm',
        icon: 'fa fa-bar-chart',
        title: '统计',
        auth: 'stats',
        url: 'stats?id={id}',
        method: 'open'
    }
]}
```

## 表格事件监听

表格的编辑、开关等事件监听：

```javascript
// 监听单元格编辑
admin.table.listenEdit(init, layFilter, tableId);

// 监听开关切换
admin.table.listenSwitch({
    filter: 'status',
    url: '{:url("modify")}'
});
```

