define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'zc.cat/index',
        add_url: 'zc.cat/add',
        edit_url: 'zc.cat/edit',
        delete_url: 'zc.cat/delete',
        export_url: 'zc.cat/export',
        modify_url: 'zc.cat/modify',
    };

    var Controller = {

        index: function () {

            var toolbar = ['refresh', 'add'];
            ea.table.render({
                init: init,
                toolbar: toolbar,
                cols: [[
                    {type: 'checkbox'},
                    {type: 'numbers', title: '序号'},
                    {field: 'title', title: '分类名称'},
                    {
                        field: 'status',
                        search: 'select',
                        selectList: ["禁用", "启用"],
                        title: '状态',
                    },
                    {field: 'create_time', title: '创建时间', search: 'date_range'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});