
# php think curd -t zc_cat -r system_dept --foreignKey=dept_id --primaryKey=id
drop table if exists `sq_zc_cat`;
CREATE TABLE `sq_zc_cat`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `dept_id`     int(10) unsigned NOT NULL,
    `title`       varchar(20)      NOT NULL COMMENT '名称',
    `status`      tinyint(1) unsigned       DEFAULT 1 COMMENT '状态 {radio} (0:禁用,1:启用)',
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `title` (`title`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='资产分类';


# php think curd -t zc_asset -r zc_cat --foreignKey=cat_id --primaryKey=id
drop table if exists `sq_zc_asset`;
CREATE TABLE `sq_zc_asset`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `cat_id`      int(10) unsigned NOT NULL DEFAULT 0,
    `dept_id`     int(10) unsigned NOT NULL DEFAULT 0,
    `title`       varchar(20)      NOT NULL COMMENT '名称',
    `addr`        text COMMENT '位置',
    `area`        varchar(50) COMMENT '面积',
    `status`      tinyint(1) unsigned       DEFAULT 0 COMMENT '状态 {radio} (0:闲置,1:出租)',
    `remark`      text COMMENT '备注',
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `title` (`title`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='资产管理';

# php think curd -t zc_asset_order -r zc_asset --foreignKey=asset_id --primaryKey=id
drop table if exists `sq_zc_asset_order`;
CREATE TABLE `sq_zc_asset_order`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `asset_id`    int(10) unsigned NOT NULL default 0,
    `dept_id`     int(10) unsigned NOT NULL,
    `zy_comp`     varchar(100)     NOT NULL default '' COMMENT '租用单位名称',
    `area`        varchar(100)     NOT NULL default '' COMMENT '合同面积',
    `start_date`  int(10)          NOT NULL default 0 COMMENT '起始时间{date}(date)',
    `end_date`    int(10)          NOT NULL default 0 COMMENT '到期时间{date}(date)',
    `days`        int(10)          NOT NULL default 0 COMMENT '总天数',
    `price`       decimal(10, 2)   NOT NULL default 0 COMMENT '月租金',
    `ya_price`    decimal(10, 2)   NOT NULL default 0 COMMENT '押金',
    `total`       decimal(10, 2)   NOT NULL default 0 COMMENT '总价',
    `zhouqi`      varchar(20)      NOT NULL default '' COMMENT '支付周期',
    `pay_type`    varchar(50)      NOT NULL default '' COMMENT '付费方式',
    `files`       text COMMENT '附件',
    `status`      tinyint(1) unsigned       DEFAULT 0 COMMENT '是否欠费 {radio} (0:否,1:是)',
    `type`        tinyint(1) unsigned       DEFAULT 0 COMMENT '类型 {radio}(0:自用,1:上交)',
    `remark`      text COMMENT '备注',
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='台账管理';


# php think curd -t zc_asset_order_msg -r zc_asset_order --foreignKey=order_id --primaryKey=id
drop table if exists `sq_zc_asset_order_msg`;
CREATE TABLE `sq_zc_asset_order_msg`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `asset_id`    int(10) unsigned NOT NULL default 0,
    `order_id`    int(10) unsigned NOT NULL default 0,
    `title`       varchar(20)      NOT NULL COMMENT '标题',
    `content`     text COMMENT '内容',
    `desc`        text COMMENT '处理内容',
    `dept_id`     int(10) unsigned NOT NULL,
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `title` (`title`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='台账维护';

# php think curd -t zc_asset_order_event -r zc_asset_order --foreignKey=order_id --primaryKey=id
drop table if exists `sq_zc_asset_order_event`;
CREATE TABLE `sq_zc_asset_order_event`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `asset_id`    int(10) unsigned NOT NULL default 0,
    `order_id`    int(10) unsigned NOT NULL default 0,
    `title`       varchar(20)      NOT NULL COMMENT '标题',
    `content`     text COMMENT '内容',
    `desc`        text COMMENT '处理内容',
    `dept_id`     int(10) unsigned NOT NULL,
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `title` (`title`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='台账事件';


drop table if exists `sq_zc_demp`;
CREATE TABLE `sq_zc_demp`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `no`     varchar(60) NOT NULL default '' COMMENT '编号',
    `title`       varchar(20)      NOT NULL COMMENT '名称',
    `name` varchar(30) NOT NULL default '',
    `create_by`   int(10) unsigned NOT NULL default 0,
    `create_time` int(11)                   DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11)                   DEFAULT NULL COMMENT '更新时间',
    `delete_time` int(11)                   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `title` (`title`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='资产分类';