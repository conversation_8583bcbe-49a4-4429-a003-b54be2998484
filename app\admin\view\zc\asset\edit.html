<script type="text/javascript">

    function loadScript() {
        var script = document.createElement("script");
        script.src = "https://api.map.baidu.com/api?v=2.0&ak=tCNPmUfNmy4nTR3VYW71a6IgyWMaOSUb&callback=initialize";
        document.body.appendChild(script);
    }

    window.onload = loadScript;
</script>
<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">
        
        <div class="layui-form-item">
            <label class="layui-form-label">分类</label>
            <div class="layui-input-block">
                <select name="cat_id" lay-verify="required">
                    <option value="">请选择</option>
                    {foreach $getCatList as $k=>$v}
                    <option value="{$v.id}" {if $row.cat_id == $v.id}selected=""{/if}>{$v.title}</option>
                    {/foreach}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-block">
                <input type="text" name="title" class="layui-input" lay-verify="required" placeholder="请输入名称" value="{$row.title|default=''}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">位置</label>
            <div class="layui-input-block">
                <input type="text" name="addr" class="layui-input"  placeholder="请输入位置" value="{$row.addr|default=''}">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">经度</label>
                <div class="layui-input-inline" style="width: 100px;">
                    <input type="text" name="longitude" class="layui-input" lay-verify="required"
                           placeholder="请输入经度" value="{$row.longitude|default=''}">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="text-align: left;width: auto;">纬度</label>
                <div class="layui-input-inline" style="width: 100px;">
                    <input type="text" name="latitude" class="layui-input" lay-verify="required" placeholder="请输入纬度"
                           value="{$row.latitude|default=''}">
                </div>
            </div>
            <div class="layui-inline">
                <div id="locationBtn" class="layui-btn layui-btn-normal"><i class="layui-icon layui-icon-location"></i>地图选点</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">面积</label>
            <div class="layui-input-block">
                <input type="text" name="area" class="layui-input"  placeholder="请输入面积" value="{$row.area|default=''}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">平面图</label>
            <div class="layui-input-block layuimini-upload">
                <input name="image" class="layui-input layui-col-xs6"   placeholder="请上传平面图" value="{$row.image|default=''}">
                <div class="layuimini-upload-btn">
                    <span><a class="layui-btn" data-upload="image" data-upload-number="one" data-upload-exts="png|jpg|ico|jpeg" data-upload-icon="image"><i class="fa fa-upload"></i> 上传</a></span>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">宣传视频</label>
            <div class="layui-input-block layuimini-upload">
                <input name="video" class="layui-input layui-col-xs6"   placeholder="请上传宣传视频" value="{$row.video|default=''}">
                <div class="layuimini-upload-btn">
                    <span><a class="layui-btn" data-upload="video" data-upload-number="one" data-upload-exts="mp4" data-upload-icon="video"><i class="fa fa-upload"></i> 上传</a></span>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                {foreach $getStatusList as $k=>$v}
                <input type="radio" name="status" value="{$k}" title="{$v}" {in name="k" value="$row.status"}checked=""{/in}>
                {/foreach}
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">单价</label>
            <div class="layui-input-block">
                <input type="number" name="price" class="layui-input" lay-verify="required" placeholder="请输入单价" value="{$row.price|default=''}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">年价</label>
            <div class="layui-input-block">
                <input type="number" name="year_price" class="layui-input" lay-verify="required" placeholder="请输入年价" value="{$row.year_price|default=''}">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">介绍</label>
            <div class="layui-input-block">
                <textarea name="remark" class="layui-textarea editor" rows="20"  placeholder="请输入介绍">{$row.remark|raw|default=''}</textarea>
            </div>
        </div>
        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit>确认</button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
        </div>

    </form>
</div>