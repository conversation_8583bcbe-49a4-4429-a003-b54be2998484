define(["jquery", "easy-admin","location","layarea"], function ($, ea) {

    var location = layui.location,
        layarea = layui.layarea,
        element = layui.element;

    function areaRender() {
        layarea.render({
            elem: '#area-picker',
            change: function (res) {
                //选择结果
                console.log(res);
            }
        });
    }

    function locationRender() {
        var longitudeEle = $("[name='longitude']"),
            longitude = longitudeEle.val(), latitudeEle = $("[name='latitude']"),
            latitude = latitudeEle.val();

        location.render("#locationBtn", {
            type: 1,
            apiType: "baiduMap",
            coordinate: "gaodeMap",
            mapType: 0,
            zoom: 15,
            title: '地图选点',
            init: function () {
                return {
                    longitude: longitude ? longitude : locationData.lng,
                    latitude: latitude ? latitude : locationData.lat
                };
            },
            success: function (data) {
                longitudeEle.val(data.lng);
                latitudeEle.val(data.lat);
            }
        });
    }

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'zc.asset/index',
        add_url: 'zc.asset/add',
        edit_url: 'zc.asset/edit',
        delete_url: 'zc.asset/delete',
        export_url: 'zc.asset/export',
        modify_url: 'zc.asset/modify',
    };

    var locationData = {}

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh', 'add'],
                cols: [[
                    {type: 'checkbox'},
                    {type: 'numbers', title: '序号'},
                    {field: 'cat.title', title: '分类'},
                    {field: 'title', title: '名称'},
                    {field: 'addr', title: '位置'},
                    /*{field: 'lat', title: '纬度'},
                    {field: 'lng', title: '经度'},*/
                    {field: 'area', title: '面积', search: false},
                    {field: 'image', title: '平面图',search: false, templet: ea.table.image},
                    {field: 'status', 'title': '状态', search: 'select', selectList: ["闲置", "出租"]},
                    {field: 'price', title: '单价', search: false},
                    {field: 'year_price', title: '年价', search: false},
                    /*{field: 'remark', title: '介绍', search: false},*/
                    {field: 'creator_name', title: '创建人', search: false},
                    {field: 'create_time', title: '创建时间', search: 'date_range'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            areaRender();
            locationRender();
            ea.listen();
        },
        edit: function () {
            areaRender();
            locationRender();
            ea.listen();
        },
    };
    return Controller;
});