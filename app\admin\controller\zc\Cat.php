<?php

namespace app\admin\controller\zc;

use app\common\controller\AdminController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use think\App;

/**
 * @ControllerAnnotation(title="资产分类")
 */
class Cat extends AdminController
{

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->model = new \app\admin\model\ZcCat();
        
        $this->assign('getStatusList', $this->model->getStatusList());

    }

    
    /**
     * @NodeAnotation(title="列表")
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            if (input('selectFields')) {
                return $this->selectList();
            }
            list($page, $limit, $where) = $this->buildTableParames();
            $count = $this->model
                ->with<PERSON>oin(['creator'], 'LEFT')
                ->where($where)
                ->count();
            $list = $this->model
                ->with<PERSON>oin(['creator'], 'LEFT')
                ->where($where)
                ->page($page, $limit)
                ->order($this->sort)
                ->select();
            $data = [
                'code'  => 0,
                'msg'   => '',
                'count' => $count,
                'data'  => $list,
            ];
            return json($data);
        }
        return $this->fetch();
    }
	
	/**
	 * @NodeAnotation(title="添加")
	 */
	public function add()
	{
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $this->model->saveFromCreateId($post);
			} catch (\Exception $e) {
				$this->error('保存失败:'.$e->getMessage());
			}
			$save ? $this->success('保存成功') : $this->error('保存失败');
		}
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="编辑")
	 * @param $id
	 * @return
	 */
	public function edit($id)
	{
		$row = $this->model->getInfo($id);
		empty($row) && $this->error('数据不存在');
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $row->save($post);
			} catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save ? $this->success('保存成功') : $this->error('保存失败');
		}
		$this->assign('row', $row);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="删除")
	 */
	public function delete($id)
	{
		$adminInfo = session('admin');
		$deptId = $adminInfo['dept_id'];
		$where = [];
		if ($adminInfo['id'] != 1) {
			$where[] = ['dept_id', '=', $deptId];
		}
		$dataScope = $this->model->getThisModelDataScope();
		if ($dataScope !== true) {
			$where[]   = [
				'create_by',
				'in',
				$dataScope
			];
		}
		$row = $this->model->where($where)->whereIn('id', $id)->select();
		$row->isEmpty() && $this->error('数据不存在');
		try {
			$save = $row->delete();
		} catch (\Exception $e) {
			$this->error('删除失败');
		}
		$save ? $this->success('删除成功') : $this->error('删除失败');
	}
}