<?php

use think\facade\Env;

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => Env::get('cache.driver', 'file'),

    // 缓存连接方式配置
    'stores'  => [
        'file'  => [
            // 驱动方式
            'type'       => 'File',
            // 缓存保存目录
            'path'       => '',
            // 缓存前缀
            'prefix'     => '',
            // 缓存有效期 0表示永久缓存
            'expire'     => Env::get('cache.expire', '0'),
            // 缓存标签前缀
            'tag_prefix' => Env::get('cache.prefix', 'zc_'),
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => [],
        ],
        // 更多的缓存连接
        'redis' => [
            // 驱动方式
            'type' => 'redis',
            // 服务器地址
            'host' => Env::get('cache.host', '127.0.0.1'),
            //端口
            'port'     => Env::get('cache.port', '6379'),
            //密码
            'password' => Env::get('cache.password', ''),
            //选择的库
            'select'   => Env::get('cache.select', '0'),
            // 全局缓存有效期（0为永久有效）
            'expire'   => Env::get('cache.expire', '0'),
            // 缓存前缀
            'prefix'   => Env::get('cache.prefix', 'zc_'),
            'timeout'  => 0,
        ],
    ],
];
