.ew-map-select-tool {
    padding: 5px 15px;
    box-shadow: 0 1px 0 0 rgba(0, 0, 0, .05);
}
.inline-block {
    display: inline-block;
}
.layui-btn.icon-btn {
    padding: 0 10px;
}

.pull-right {
    float: right;
}

.map-select:after, .map-select:before {
    content: '';
    display: block;
    clear: both;
}

.ew-map-select-poi {
    height: 505px;
    width: 250px;
    overflow-x: hidden;
    overflow-y: auto;
    float: left;
	position: relative;
    display: block;
    box-sizing: border-box;
}

.ew-map-select-search-list-item {
    padding: 10px 30px 10px 15px;
    border-bottom: 1px solid #e8e8e8;
    cursor: pointer;
    position: relative;
}

.ew-map-select-search-list-item .ew-map-select-search-list-item-title {
    font-size: 14px;
    color: #262626;
}

.ew-map-select-search-list-item .ew-map-select-search-list-item-address {
    font-size: 12px;
    color: #595959;
    padding-top: 5px;
}

.ew-map-select-search-list-item-icon-ok {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
}

#ew-map-select-tips {
    position: absolute;
    z-index: 999;
    background: #fff;
    max-height: 430px;
    overflow: auto;
    top: 48px;
    left: 56px;
    width: 280px;
    box-shadow: 0 2px 4px rgba(0,0,0,.12);
    border: 1px solid #d2d2d2;
}

#ew-map-select-tips .ew-map-select-search-list-item {
    padding: 10px 15px 10px 35px;
}

.ew-map-select-search-list-item-icon-search {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
}

.ew-map-select-search-list-item .ew-map-select-search-list-item-title {
    font-size: 14px;
    color: #262626;
}

.ew-map-select-search-list-item .ew-map-select-search-list-item-address {
    font-size: 12px;
    color: #595959;
    padding-top: 5px;
}

.cur-load0 {
    display: none;
    background: url('./img/location.cur');
}

.cur-load1 {
    display: none;
    background: url('./img/location_blue.cur');
}
