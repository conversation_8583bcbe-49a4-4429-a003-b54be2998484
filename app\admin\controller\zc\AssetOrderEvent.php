<?php

namespace app\admin\controller\zc;

use app\admin\model\ZcAssetOrder;
use app\common\controller\AdminController;
use DateInterval;
use DateTime;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use think\App;
use think\facade\Db;
use zjkal\TimeHelper;

/**
 * @ControllerAnnotation(title="特请处理")
 */
class AssetOrderEvent extends AdminController
{
	
	//    use \app\admin\traits\Curd;
	
	public function __construct(App $app)
	{
		parent::__construct($app);
		
		$this->model = new \app\admin\model\ZcAssetOrderEvent();
		
	}
	
	
	/**
	 * @NodeAnotation(title="列表")
	 */
	public function index()
	{
		$oid = input('oid/d', 0);
		if ($this->request->isAjax()) {
			/*if (input('selectFields')) {
				return $this->selectList();
			}*/
			list($page, $limit, $where) = $this->buildTableParames();
			$dataScope = $this->model->getThisModelDataScope();
			if ($dataScope !== true) {
				$where[] = [
					'zc_asset_order_event.create_by',
					'in',
					$dataScope
				];
			}
			$where[] = [
				'zc_asset_order_event.order_id',
				'=',
				$oid
			];
			$count   = $this->model->withJoin('asset', 'LEFT')
			                       ->where($where)
			                       ->count();
			$list    = $this->model->withJoin('asset', 'LEFT')
			                       ->where($where)
			                       ->page($page, $limit)
			                       ->order($this->sort)
			                       ->select();
			$data    = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		$this->assign('oid', $oid);
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="添加")
	 */
	public function add()
	{
		$oid     = input('oid/d', 0);
		$oidInfo = ZcAssetOrder::with(['asset'])
		                       ->where('id', $oid)
		                       ->findOrEmpty();
		if ($oidInfo->isEmpty()) {
			$this->error('台账不存在');
		}
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			Db::startTrans();
			try {
				$type = $post['type'];
				if ($type == 1) {
					if (empty($post['event_start_date']) || empty($post['event_end_date'])) {
						throw new \Exception('请选择免租日期');
					}
					if (strtotime($post['event_start_date']) < strtotime($oidInfo['start_date'])) {
						throw new \Exception('免租日期不能小于台账开始日期');
					}
					if (strtotime($post['event_end_date']) > strtotime($oidInfo['end_date'])) {
						throw new \Exception('免租日期不能大于台账结束日期');
					}
					$days               = TimeHelper::diffDays($post['event_start_date'], $post['event_end_date']);
					$post['total_days'] = $days;
					if ($days > 0) {
						$endDate = new DateTime($oidInfo['end_date']);
						
						$endDate->modify('+' . $days . ' days');
						$newEndDate = $endDate->format('Y-m-d');
						$save       = $oidInfo->save([
							'end_date' => $newEndDate,
						]);
					}
					$post['price'] = 0;
				}
				else {
					$post['event_start_date'] = 0;
					$post['event_end_date']   = 0;
					$save                     = $oidInfo->save([
						'mianzu' => Db::raw('mianzu+' . bcadd($post['price'], 0, 2)),
					]);
				}
				$save = $this->model->saveFromCreateId($post);
				if ($save) {
					Db::commit();
				}
				else {
					Db::rollback();
				}
			}
			catch (\Exception $e) {
				Db::rollback();
				$this->error('保存失败:' . $e->getMessage());
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('oidInfo', $oidInfo);
		return $this->fetch();
	}
}