define(["jquery", "easy-admin"], function ($, ea) {

    var oid = $("#oid").val() || 0,form = layui.form;

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'zc.asset_order_event/index?oid=' + oid,
        add_url: 'zc.asset_order_event/add?oid=' + oid,
        edit_url: 'zc.asset_order_event/edit',
        delete_url: 'zc.asset_order_event/delete',
        export_url: 'zc.asset_order_event/export',
        modify_url: 'zc.asset_order_event/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh', 'add'],
                defaultToolbar: 'close',
                cols: [[
                    {type: 'checkbox'},
                    {type: 'numbers', title: '序号'},
                    {field: 'asset.title', width: 150,search: false, title: '资产名称'},
                    {field: 'price', title: '金额', search: false,},
                    {field: 'image', title: '减免凭证图片', search: false, templet: ea.table.image},
                    {field: 'event_start_date', search: false, title: '免租开始日期'},
                    {field: 'event_end_date', search: false, title: '免租结束日期'},
                    {field: 'creator_name', search: false, title: '创建人'},
                    {field: 'create_time', search: 'date_range', title: '创建时间'},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var old = 0;
            form.on('radio(type_filter)',function (data) {
                var elem = data.elem;
                var value = elem.value;
                if (value == 0) {
                    $("#price_box").show();
                    $("#date_box").hide();
                } else {
                    $("#price_box").hide();
                    $("#date_box").show();
                }
                /*if (value != old) {
                    old = value;
                    $("input[name='image']").val('');
                    $("#bing-image").html('');
                }*/
            })
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});