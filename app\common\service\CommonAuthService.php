<?php


namespace app\common\service;

use app\common\constants\AdminConstant;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Config;
use think\facade\Db;

/**
 * 权限验证服务
 * Class AuthService
 * @package app\common\service
 */
abstract class CommonAuthService
{

    /**
     * 用户ID
     * @var null
     */
    protected $adminId = null;

    /**
     * 管理员信息
     * @var array|\think\Model|null
     */
    protected $adminInfo;


    /**
     * 默认配置
     * @var array
     */
    protected $config;

    /**
     * 所有节点信息
     * @var array
     */
    protected $nodeList;

    /**
     * 管理员所有授权节点
     * @var array
     */
    protected $adminNode;

    /***
     * 构造方法
     * AuthService constructor.
     * @param null $adminId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function __construct($adminId = null)
    {
        $this->adminId = $adminId;
        $this->adminInfo = $this->getAdminInfo();
        $this->nodeList = $this->getNodeList();
        $this->adminNode  = $this->getAdminNode();
        return $this;
    }

    /**
     * 检测检测权限
     * @param null $node
     * @return bool
     */
    public function checkNode($node = null)
    {
        // 判断是否为超级管理员
        if ($this->adminId == AdminConstant::SUPER_ADMIN_ID) {
            return true;
        }
        // 判断权限验证开关
        if ($this->config['auth_on'] == false) {
            return true;
        }
        // 判断是否需要获取当前节点
        if (empty($node)) {
            $node = $this->getCurrentNode();
        } else {
            $node = $this->parseNodeStr($node);
        }
        // 判断是否加入节点控制，优先获取缓存信息
        if (!isset($this->nodeList[$node])) {
            return Config::get('admin.default_auth_check');
        }
        $nodeInfo = $this->nodeList[$node];
        if ($nodeInfo['is_auth'] == 0) {
            return true;
        }
        // 用户验证，优先获取缓存信息
        if (empty($this->adminInfo) || $this->adminInfo['status'] != 1 || empty($this->adminInfo['auth_ids'])) {
            return false;
        }
        // 判断该节点是否允许访问
        if (in_array($node, $this->adminNode)) {
            return true;
        }
        return false;
    }

    /**
     * 获取当前节点
     * @return string
     */
    public function getCurrentNode()
    {
        return $this->parseNodeStr(request()->controller() . '/' . request()->action());
    }

    /**
     * 获取当前管理员所有节点
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getAdminNode()
    {
        $nodeList = [];
        $adminInfo = $this->getAdminInfo();

        if (!empty($adminInfo) && $adminInfo['status'] != 1) {
            return $nodeList;
        }

        if (!empty($adminInfo) && !empty($adminInfo['auth_ids'])) {
            $buildAuthSql = Db::name($this->config['system_auth'])
                ->distinct()
                ->whereIn('id', $adminInfo['auth_ids'])
                ->field('id')
                ->buildSql();
            $buildAuthNodeSql = Db::name($this->config['system_auth_node'])
                ->distinct()
                ->where("auth_id IN {$buildAuthSql}")
                ->field('node_id')
                ->buildSql();
            $nodeList = Db::name($this->config['system_node'])
                ->distinct()
                ->where("id IN {$buildAuthNodeSql}")
                ->column('node');
        }
        return $nodeList;
    }

    /**
     * 获取所有节点信息
     * @time 2021-01-07
     * @return array
     */
    public function getNodeList()
    {
        return  Db::name($this->config['system_node'])
            ->column('id,node,title,type,is_auth', 'node');
    }

    /**
     * 获取管理员信息
     * @time 2021-01-07
     * @return array|\think\Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getAdminInfo()
    {
        return  Db::name($this->config['system_admin'])
            ->where('id', $this->adminId)
            ->find();
    }

    /**
     * 驼峰转下划线规则
     * @param string $node
     * @return string
     */
    public function parseNodeStr(string $node)
    {
        $array = explode('/', $node);
        foreach ($array as $key => $val) {
            if ($key == 0) {
                $val = explode('.', $val);
                foreach ($val as &$vo) {
                    $vo = \think\helper\Str::snake(lcfirst($vo));
                }
                $val = implode('.', $val);
                $array[$key] = $val;
            }
        }
        return implode('/', $array);
    }

    /**
     * @param array $config
     */
    public function setConfig(array $config)
    {
        $this->config = $config;
        return $this;
    }
}
