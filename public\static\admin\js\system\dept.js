define(["jquery", "easy-admin","treetable"], function ($, ea) {

    var treetable = layui.treetable;

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'system.dept/index',
        add_url: 'system.dept/add',
        edit_url: 'system.dept/edit',
        delete_url: 'system.dept/delete',
        /*export_url: 'system.dept/export',
        modify_url: 'system.dept/modify',*/
    };

    return {

        index: function () {


            ea.table.render({
                init: init,
                toolbar: ['refresh','add'],
                cols: [[
                    {type: 'checkbox'},
                    // {field: 'id', title: '主键'},
                    /*{field: 'parent_id', search: false, title: '所属部门'},*/
                    // {field: 'level', title: '组级集合'},
                    {field: 'name', title: '单位名称'},
                    // {field: 'keshi', title: '科室名称'},
                    {field: 'leader', title: '负责人'},
                    {field: 'phone', title: '联系电话'},
                    {
                        field: 'status', title: '状态', search: 'select',
                        selectList: ["禁用", "启用"]
                    },
                    // {field: 'sort', title: '排序', search: false},
                    {field: 'create_time', search: false, title: '创建时间'},
                    // {field: 'remark', title: '备注', templet: ea.table.text},
                    {width: 160, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
});