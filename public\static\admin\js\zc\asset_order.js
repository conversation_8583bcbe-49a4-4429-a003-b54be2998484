define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'zc.asset_order/index',
        add_url: 'zc.asset_order/add',
        edit_url: 'zc.asset_order/edit',
        delete_url: 'zc.asset_order/delete',
        export_url: 'zc.asset_order/export',
        modify_url: 'zc.asset_order/modify',
        detail_url: 'zc.asset_order/detail',
    };

    var element = layui.element, table = layui.table;

    return {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh', 'add', 'export'],
                defaultToolbar: 'close',
                cols: [[
                    { type: 'checkbox' },
                    { type: 'numbers', title: '序号' },
                    { field: 'asset.title', width: 150, title: '资产名称' },
                    {
                        width: 150, title: '进度', templet: function (d) {
                            const progress = d.progress;
                            const arr = ['layui-bg-primary', 'layui-bg-orange', 'layui-bg-red'];
                            return `<div class="layui-progress layui-progress-big" lay-showPercent="true">
  <div class="layui-progress-bar ${arr[d.progress_type]}" lay-percent="${progress}%"></div>
</div>`
                        }
                    },
                    {
                        field: 'is_check',
                        width: 100,
                        search: 'select',
                        selectList: ["未审核", "已审核", '已作废'],
                        title: '状态',
                        templet: function (d) {
                            if (d.is_check === 2) {
                                return `<span style="color: #ffc96d">已作废</span>`;
                            }
                            const title = d.is_check === 0 ? '未审核' : '已审核';
                            return `<span style="color: ${title === '未审核' ? '#ff5722' : '#16baaa'}">${title}</span>`;
                        }
                    },
                    {
                        field: 'dept.name',
                        width: 100,
                        title: '代管单位'
                    },
                    {
                        field: 'qiandings.name',
                        width: 100,
                        title: '合同签订方',
                        templet: function (d) {
                            return d.qiandings && d.qiandings.name ? d.qiandings.name : '咸运集团';
                        }
                    },
                    { field: 'zulin.title', width: 150, title: '租用单位名称' },
                    { field: 'contract_no', width: 150, title: '合同编号' },
                    { field: 'ya_price', width: 120, title: '押金', search: false },
                    { field: 'area', width: 120, title: '面积', search: false },
                    { field: 'start_date', width: 150, title: '结租日期', search: false },
                    { field: 'end_date', width: 150, title: '到期日期', search: false, },
                    {
                        field: 'end_date', hide: true, width: 150, title: '是否到期', search: 'select',
                        selectList: ["否", "是",],
                    },
                    { field: 'price', width: 120, title: '租赁单价', search: false },
                    { field: 'c_total', width: 120, title: '租赁总价', search: false },
                    {
                        field: 'pay_type',
                        search: 'select',
                        selectList: ["月付", "季付", "半年付", "年付", "一次性结清"],
                        title: '付费方式',
                        width: 120,
                    },
                    { field: 'qianfei', width: 120, title: '租金欠费金额', search: false },
                    { field: 'shangjiao', width: 120, title: '上交集团金额', search: false },
                    { field: 'ziyong', width: 120, title: '自用金额', search: false },
                    { field: 'jingbanren', width: 200, title: '经办人信息', search: false },
                    { field: 'zhinajin', width: 120, title: '滞纳金总额', search: false },
                    { field: 'leiji', width: 120, title: '累计欠费', search: false },
                    { field: 'remark', width: 200, title: '备注', search: false },
                    { field: 'creator_name', width: 130, title: '创建人', search: false },
                    { field: 'create_time', width: 150, title: '创建时间', search: 'date_range' },
                    { width: 300, title: '操作', fixed: 'right', templet: "#operatTpl" },
                ]],
                done: function (res, curr, count, origin) {
                    element.render('progress')
                },
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
        check: function () {
            ea.listen();
        },
        detail: function () {
            ea.listen();
        },
        list: function () {
            var type = $('#type_box').val();
            var dataType = $('#data_type').val();
            var year = $('#year').val();
            ea.table.render({
                url: ea.url('zc.asset_order/list') + '?type=' + type + '&data_type=' + dataType + '&year=' + year,
                toolbar: ['refresh'],
                defaultToolbar: 'close',
                page: false, // 禁用ea的分页
                cols: [[
                    { field: 'asset.title', title: '资产名称', search: false },
                    { field: 'zulin.title', title: '租用单位名称', search: false },
                    {
                        field: 'dept.name',
                        title: '代管单位', search: false
                    },
                    { field: 'yingshou', title: '应缴金额', search: false },
                    { field: 'leiji', title: '欠费金额', search: false },
                    { field: 'qianfei', title: '历史欠费金额', search: false },
                    { field: 'shishou', title: '实收金额', search: false },
                    { field: 'end_date', title: '到期时间', search: false },
                    { title: '操作', fixed: 'right', templet: "#operatTpl" },
                ]],
                done: function (res, curr, count) {

                    var currentDataType = $('#data_type').val();
                    console.log('当前dataType:', currentDataType); // 调试用
                    // 处理数据，展平嵌套字段
                    var processedData = res.data.map(function (item) {
                        return {
                            asset_title: item.asset ? item.asset.title : '',
                            zulin_title: item.zulin ? item.zulin.title : '',
                            dept_name: item.dept ? item.dept.name : '',
                            yingshou: item.yingshou,
                            leiji: item.leiji,
                            qianfei: item.qianfei,
                            shishou: item.shishou,
                            end_date: item.end_date,
                            // 保留原始数据用于操作按钮
                            _original: item
                        };
                    });

                    // 根据dataType定义不同的列配置
                    var baseCols = [
                        { field: 'asset_title', title: '资产名称' },
                        { field: 'zulin_title', title: '租用单位名称' },
                        { field: 'dept_name', title: '代管单位' }
                    ];

                    var dynamicCols = [];
                    switch (dataType) {
                        case 'daoqi':
                            // 到期显示全部字段
                            dynamicCols = [
                                { field: 'yingshou', title: '应缴金额' },
                                { field: 'leiji', title: '欠费金额' },
                                { field: 'qianfei', title: '历史欠费金额' },
                                { field: 'shishou', title: '实收金额' },
                                { field: 'end_date', title: '到期时间' }
                            ];
                            break;
                        case 'yingshou':
                            // 应收只显示应缴金额
                            dynamicCols = [
                                { field: 'yingshou', title: '应缴金额' },
                                { field: 'end_date', title: '到期时间' }
                            ];
                            break;
                        case 'shishou':
                            // 实收只显示实收金额
                            dynamicCols = [
                                { field: 'shishou', title: '实收金额' },
                                { field: 'end_date', title: '到期时间' }
                            ];
                            break;
                        case 'qianfei':
                            // 欠费显示欠费和历史欠费
                            dynamicCols = [
                                { field: 'leiji', title: '欠费金额' },
                                { field: 'qianfei', title: '历史欠费金额' },
                                { field: 'end_date', title: '到期时间' }
                            ];
                            break;
                        default:
                            // 默认显示全部
                            dynamicCols = [
                                { field: 'yingshou', title: '应缴金额' },
                                { field: 'leiji', title: '欠费金额' },
                                { field: 'qianfei', title: '历史欠费金额' },
                                { field: 'shishou', title: '实收金额' },
                                { field: 'end_date', title: '到期时间' }
                            ];
                    }

                    // 合并基础列和动态列，最后加上操作列
                    var finalCols = baseCols.concat(dynamicCols).concat([
                        { title: '操作', fixed: 'right', templet: "#operatTpl" }
                    ]);

                    // 使用LayUI原生分页
                    table.render({
                        elem: '#currentTable',
                        data: processedData,
                        page: {
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                            groups: 5,
                            first: '首页',
                            last: '尾页',
                            limits: [15,30]
                        },
                        limit: 15,
                        cols: [finalCols]
                    });
                }
            });

            ea.listen();
        },
        dlist: function () {
            ea.table.render({
                url: ea.url('zc.asset_order/dlist'),
                toolbar: ['refresh'],
                defaultToolbar: 'close',
                cols: [[
                    { field: 'asset.title', title: '资产名称', search: false },
                    { field: 'zulin.title', title: '租用单位名称', search: false },
                    {
                        field: 'dept.name',
                        title: '代管单位', search: false
                    },
                    { field: 'leiji', title: '欠费金额', search: false },
                    { field: 'end_date', title: '到期时间', search: 'date_range' },
                    { title: '操作', fixed: 'right', templet: "#operatTpl" },
                ]],
            });

            ea.listen();
        },
        rlist: function () {
            ea.table.render({
                url: ea.url('zc.asset_order/rlist'),
                toolbar: ['refresh'],
                defaultToolbar: 'close',
                cols: [[
                    { field: 'asset.title', title: '资产名称', search: false },
                    { field: 'zulin.title', title: '租用单位名称', search: false },
                    {
                        field: 'dept.name',
                        width: 100,
                        title: '代管单位'
                    },
                    { field: 'leiji', title: '欠费金额', search: false },
                    { field: 'end_date', title: '到期时间', search: false },
                    { title: '操作', fixed: 'right', templet: "#operatTpl" },
                ]],
            });

            ea.listen();
        },
    };
});
