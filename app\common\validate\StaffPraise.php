<?php
declare (strict_types = 1);

namespace app\common\validate;

use think\Validate;

class StaffPraise extends Validate
{
    /**
     * @var array
     */
    protected $rule = [
        'user_title' => 'require',
        'user_mobile' => 'require|mobile',
        'start_time' => 'require|dateFormat:Y-m-d H:i:s',
        'end_time' => 'require|dateFormat:Y-m-d H:i:s|gt:start_time',
        'praise_id'=>'require',
        'user_avatar'=>'require',
        'user_img'=>'array',
    ];

    /**
     * @var array
     */
    protected $message = [
        'user_title' => '请输入会员姓名/昵称',
        'user_mobile' => '请输入手机号',
        'user_mobile.mobile' => '手机号格式不正确',
        'start_time.require' => '请选择开始时间',
        'start_time.dateFormat' => '开始时间格式不正确',
        'end_time.require' => '请选择结束时间',
        'end_time.dateFormat' => '结束时间格式不正确',
        'end_time.gt' => '结束时间须大于开始时间',
        'praise_id.require'=>'参数错误',
        'id.require'=>'编辑参数错误',
        'user_avatar.require'=>'请上传会员头像',
        'user_img.array'=>'请上传会员客片',
    ];

    public function sceneAdd()
    {
        return $this->remove('id', 'require');
    }


}
