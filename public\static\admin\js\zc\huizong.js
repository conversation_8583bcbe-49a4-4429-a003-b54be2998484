define(["jquery", "easy-admin", 'miniTab'], function ($, ea, miniTab) {

    var init = {
        index_url: 'zc.huizong/index',
    };

    var selectedYear = '';  // 新增全局变量保存年份

    var Controller = {

        index: function () {


            var type = 0;
            var element = layui.element, layer = layui.layer, table = layui.table, form = layui.form,
                laydate = layui.laydate;

            // DOM就绪后执行
            $(function () {
                // 初始化年份选择器
                laydate.render({
                    elem: '#year_select',
                    type: 'year',
                    trigger: 'click',
                    done: function (value, date) {
                        if (value) {
                            type = 4
                            selectedYear = value;
                            $('#type4').html(value);
                            getData(value);
                            element.tabChange('test-hash', '4');
                        }
                    }
                });
            });

            function tableRender() {
                const arr = ['qianfei', 'daoqi'];
                arr.map(item => {
                    const tableDataStr = $('#' + item + 'Data').val(),
                        tableData = tableDataStr != '' ? JSON.parse(tableDataStr) : [];
                    console.log('tableData', tableData)
                    table.render({
                        elem: '#' + item + '_table',
                        cols: [[ //标题栏
                            {field: 'asset_title', title: '资产名称'},
                            {field: 'zulin_title', title: '租赁单位名称',},
                            {field: 'dept_title', title: '代管单位',},
                            {field: 'qianfei', title: '欠费金额',},
                            {field: 'end_date', title: '到期时间',},
                        ]],
                        data: tableData,
                        page: true,
                        limits: [10],
                        limit: 10
                    });
                })
            }

            tableRender()


            element.on('tab(test-hash)', function (data) {
                if (type != data.index) {
                    type = data.index;
                    console.log('type', type)
                    if (type == 4 && selectedYear) {
                        // 年份选择的tab
                        getData(selectedYear);
                    } else {
                        getData();
                    }
                }
            });

            function getData(date = '') {
                $.ajax({
                    url: ea.url(init.index_url),
                    data: {
                        type,
                        date,
                    },
                    beforeSend: function () {
                        layer.load(2);
                    },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = [], obj;
                        if (res.code == 0) {
                            data = res.data;
                            obj = $("#type_box");
                            $(obj).find(".yingshou").html(data.yingshou || 0);
                            $(obj).find(".shishou").html(data.shishou || 0);
                            $(obj).find(".qianfei").html(data.qianfei || 0);
                            $(obj).find(".daoqi").html(data.daoqi || 0);
                        }
                    }
                })
            }

            getData()

            $("body").on("click", "#queren", function () {
                getData($('#date').val())
            })

            // 修改点击事件处理函数，统一处理所有数据点击
            function handleDataClick(dataType, title) {
                var loading = layer.load(0, {shade: false, time: 2 * 1000});
                var url = '';

                var year = $('#type4').html();
                // 根据不同数据类型设置不同的URL参数
                switch (dataType) {
                    case 'daoqi':
                        url = ea.url('zc.asset_order/list?type=' + type + '&data_type=daoqi&year=' + year);
                        break;
                    case 'yingshou':
                        url = ea.url('zc.asset_order/list?type=' + type + '&data_type=yingshou&year=' + year);
                        break;
                    case 'shishou':
                        url = ea.url('zc.asset_order/list?type=' + type + '&data_type=shishou&year=' + year);
                        break;
                    case 'qianfei':
                        url = ea.url('zc.asset_order/list?type=' + type + '&data_type=qianfei&year=' + year);
                        break;
                }

                var ele = parent.parent.layui.element;
                var checkTab = false;

                parent.parent.layui.$(".layui-tab-title li").each(function () {
                    var checkTabId = $(this).attr('lay-id');
                    if (checkTabId != null && checkTabId === url) {
                        checkTab = true;
                    }
                });

                if (!checkTab) {
                    var options = {
                        tabId: url,
                        title: title,
                        href: url,
                        isIframe: true,
                    }
                    ele.tabAdd('layuiminiTab', {
                        title: '<span class="layuimini-tab-active"></span><span>' + options.title + '</span><i class="layui-icon layui-unselect layui-tab-close">ဆ</i>'
                        ,
                        content: '<iframe width="100%" height="100%" frameborder="no" border="0" marginwidth="0" marginheight="0" src="' + options.href + '"></iframe>'
                        ,
                        id: options.tabId
                    });
                    sessionStorage.setItem('layuiminimenu_' + options.tabId, options.title);
                }

                parent.parent.layui.element.tabChange('layuiminiTab', url);
                layer.close(loading);
            }

            // 注册点击事件
            $("body").on("click", "#daoqi_click", function () {
                handleDataClick('daoqi', '到期项目列表');
            });

            $("body").on("click", "#yingshou_click", function () {
                handleDataClick('yingshou', '应收项目列表');
            });

            $("body").on("click", "#shishou_click", function () {
                handleDataClick('shishou', '实收项目列表');
            });

            $("body").on("click", "#qianfei_click", function () {
                handleDataClick('qianfei', '欠费项目列表');
            });

            $("body").on("click", "#chongzhi", function () {
                $('#date').val('')
                $("#type_box").find(".yingshou").html(0);
                $("#type_box").find(".shishou").html(0);
                $("#type_box").find(".qianfei").html(0);
                $("#type_box").find(".daoqi").html(0);
            })

            ea.listen();
        },
    };
    return Controller;
});