<?php

namespace app\index\controller;

use app\admin\model\ZcAssetOrder;
use app\admin\model\ZcTask;
use app\BaseController;
use think\facade\Log;

class Task extends BaseController
{
	public function run()
	{
		$today = ZcTask::whereDay('create_time')
		               ->findOrEmpty();
		if ($today->isEmpty()) {
			$count = 0;
			$list  = ZcAssetOrder::whereTime('end_date', '>=', date('Y-m-d'))
			                     ->select();
			foreach ($list as $item) {
				try {
					ZcAssetOrder::calcQianfei($item);
					$count++;
				}
				catch (\Exception $e) {
					Log::record('错误信息:' . $e->getMessage());
					continue;
				}
			}
			echo '处理成功:' . $count . '条数据';
			echo PHP_EOL;
			$res = $today->save([
				'create_time' => time()
			]);
		}
		else {
			echo '今日已执行';
		}
		
	}
}