<?php

namespace app\common\model;

use think\facade\Request;

class Shop extends TimeModel
{
	
	protected $name = "shop";
	
	protected $deleteTime = "delete_time";
	
	public function getStatusList()
	{
		return [
			'0' => '禁用',
			'1' => '启用',
		];
	}
	
	public static function getInfoById($id)
	{
		return self::find($id);
	}
	
	public static function getShopSetInfoById($id)
	{
		// return self::with(['setting'])->find($id)->hidden['setting'];
		return self::findOrEmpty($id);
	}
	
	public static function getShopInfoByHose()
	{
		$origin = Request::header('origin');
		if (!$origin) return [];
		$host = str_replace([
			"https://",
			"http://"
		], "", $origin);
		if (!$host) return [];
		//        $shopInfo = cache($host);
		//        if (!$shopInfo) {
		//            $shopInfo = self::where('host',$host)->find();
		//            if ($shopInfo) cache($host,$shopInfo);
		//        }
		return self::where('host', $host)
		           ->find();
	}
	
	
	public static function getShopAppList($shopId)
	{
		$info = self::where('id', $shopId)
		            ->findOrEmpty();
		if ($info->isEmpty()) {
			return [];
		}
		return $info->app_list;
	}
	
	public static function getAppList($plugins = '')
	{
		$arr = [
			[
				'id'    => 0,
				'title' => '点赞'
			],
			[
				'id'    => 1,
				'title' => '报名'
			],
			[
				'id'    => 2,
				'title' => '电子请帖'
			],
			[
				'id'    => 3,
				'title' => '订单管理'
			],
		];
		if (!empty($plugins)) {
			$pluginsArr = explode(',', $plugins);
			foreach ($arr as $k => $v) {
				if (in_array($v['id'], $pluginsArr)) {
					$arr[$k]['selected'] = true;
				}
			}
		}
		return $arr;
	}
	
	
}