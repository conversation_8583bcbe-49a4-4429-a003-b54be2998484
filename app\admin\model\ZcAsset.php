<?php

namespace app\admin\model;

use app\admin\traits\ModelTrait;
use app\common\model\TimeModel;

class ZcAsset extends TimeModel
{
	use ModelTrait;
	
    protected $name = "zc_asset";

    protected $deleteTime = "delete_time";

    
    public function cat()
    {
        return $this->belongsTo('\app\admin\model\ZcCat', 'cat_id', 'id');
    }

    
    public function getStatusList()
    {
        return ['0'=>'闲置','1'=>'出租',];
    }


}