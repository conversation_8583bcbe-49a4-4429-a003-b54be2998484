/*
Author: <PERSON>
E-mail: <EMAIL>
Date: 2014-11-05
*/
.searchable-select-hide {
    display: none;
}

.searchable-select {
    display: inline-block;
    min-width: 200px;
    max-width: 340px;
    color: #333;
    vertical-align: middle;
    position: relative;
    outline: none;
    text-align: left;
    white-space: pre-wrap;
    /*font-size: 12px;*/
    line-height: 20px;
}

.searchable-select-holder {
    padding: 4px 20px 4px 6px;
    background-color: #fff;
    border: 1px solid #eceef1;
    /* 	border-radius: 4px; */
    min-height: 34px;
    box-sizing: border-box;
    /* -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); */
    /* box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); */
    -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    width: 310px;
}

.searchable-select-caret {
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 6px;
    border-color: transparent;
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -3px;
    cursor: pointer;
    border-top-color: #c2c2c2;
    border-top-style: solid;
    transition: all .3s;
    -webkit-transition: all .3s;
}

.searchable-select-caret.selected {
    margin-top: -9px;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}

.searchable-select-dropdown {
    position: absolute;
    background-color: #fff;
    border: 1px solid #ccc;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 4px;
    border-top: none;
    top: 28px;
    left: 0;
    right: 0;
    z-index: 7;
}

.searchable-select-input {
    border: 1px solid #ccc !important;
    outline: none;
    padding: 4px !important;
    width: 100% !important;
    box-sizing: border-box;
    height: 25px !important;
    border-radius: 0 !important;
    margin: 5px 0 0 0 !important;
}

.searchable-scroll {
    margin-top: 4px;
    position: relative;
}

.searchable-scroll.has-privious {
    padding-top: 16px;
}

.searchable-scroll.has-next {
    padding-bottom: 16px;
}

.searchable-has-privious {
    top: 0;
}

.searchable-has-next {
    bottom: 0;
}

.searchable-has-privious, .searchable-has-next {
    height: 16px;
    left: 0;
    right: 0;
    position: absolute;
    text-align: center;
    z-index: 10;
    background-color: white;
    line-height: 8px;
    cursor: pointer;
}

.searchable-select-items {
    max-height: 400px;
    overflow-y: scroll;
    position: relative;
}

.searchable-select-items::-webkit-scrollbar {
    display: none;
}

.searchable-select-item {
    padding: 5px 5px;
    cursor: pointer;
    min-height: 36px;
    box-sizing: border-box;
    transition: all 0.5s ease 0s;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.searchable-select-item.hover {
    background: #3F9DFC;
    color: white;
}

.searchable-select-item.selected {
    background: #3F9DFC;
    color: white;
}