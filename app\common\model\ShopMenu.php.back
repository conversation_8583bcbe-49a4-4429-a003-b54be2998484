<?php

namespace app\common\model;

class ShopMenu extends TimeModel
{

    protected $autoWriteTimestamp = false;

    protected $name = "shop_menu";

    protected $deleteTime = "delete_time";

    public function getPidMenuList()
    {
        $list        = $this->field('id,pid,title')
            ->where([
                ['status', '=', 1],
            ])
            ->select()
            ->toArray();
        $pidMenuList = $this->buildPidMenu(0, $list);
        $pidMenuList = array_merge([
            [
                'id'    => 0,
                'pid'   => 0,
                'title' => '顶级菜单',
            ]
        ], $pidMenuList);
        return $pidMenuList;
    }

    //获取所有菜单ID数据
    public static function getMenuIdList()
    {
        $menuIdList = self::where('status', 1)->column('id');
        return $menuIdList ? implode(',', $menuIdList) : '';
    }

    public function getInitMenuList($rules)
    {
        $data     = [
            'logoInfo' => [
                'title' => getShopSysByKey('shop_school_sys', 'logo_title'),
                'image' => getShopSysByKey('shop_school_sys', 'logo_image'),
                'href'  => __url('index/index'),
            ],
            'homeInfo' => [
                'title' => '管理首页',
                'icon'  => '',
                'href'  => __url('index/welcome')
            ],
            'menuInfo'=>[]
        ];
        $menuList = self::getAllMenuList();
        if (!empty($menuList)) {
            $data['menuInfo'] = $this->buildInitTreeMenu($menuList);
        }
        return $data;
    }

    //获取全部菜单
    public static function getAllMenuList()
    {
        $shopInfo = session('shop');
        $model = new self();
        $where = [
            ['status','=',1],
        ];
        if (!isset($shopInfo['praise_status']) || $shopInfo['praise_status'] == 0) {
            $where[] = ['id','<>',296];
        }
        if (!isset($shopInfo['enroll_opened']) || $shopInfo['enroll_opened'] == 0) {
            $where[] = ['id','<>',297];
        }
        return $model->where($where)->order(['sort'=>'desc'])->select()->toArray();
    }

    public static function checkMenu()
    {
        $route = app('request')->controller(true) . '/';
        return self::whereLike('href', $route.'%')->where('status', 1)->column('id');
    }

    //获取layui多选框数据
    public function getGroupMenuList($adminRules)
    {
        $list        = $this->field('id,pid,title')
            ->where([
                ['status', '=', 1],
            ])
            ->select()->toArray();
        $pidMenuList = [];
        if (!empty($list)) {
            $ruleArr = explode(',', $adminRules);
            foreach ($list as $key => &$value) {
                if (in_array($value['id'], $ruleArr)) $value['selected'] = true;
            }
            $pidMenuList = $this->buildGroupMenu($list);
        }
        return $pidMenuList;
    }

    //站点添加/编辑获取后端权限
    public function getShopUserAuthList()
    {
        $list        = $this->field('id,pid,title')
            ->where([
                ['status', '=', 1],
                ['pid', '<>', 0],
            ])
            ->select()->toArray();
        return $this->buildGroupMenu($list);
    }

    //获取shop角色组树形数据
    protected function buildGroupMenu($list)
    {
        $items = array();
        foreach ($list as $v) {
            $items[$v['id']] = $v;
        }
        $tree = array();
        foreach ($items as $k => $item) {
            if (isset($items[$item['pid']])) {
                $items[$item['pid']]['children'][] = &$items[$k];
            }
            else {
                $tree[] = &$items[$k];
            }
        }
        return $tree;
    }

    //获取init首页属性菜单数据
    protected function buildInitTreeMenu($list)
    {
        $items = array();
        foreach ($list as $v) {
            $items[$v['id']] = $v;
        }
        $tree = array();
        foreach ($items as $k => $item) {
            if (isset($items[$item['pid']])) {
                $items[$k]['href']              = !empty($items[$k]['href']) ? __url($items[$k]['href']) : '';
                $items[$item['pid']]['child'][] = &$items[$k];
            }
            else {
                $tree[] = &$items[$k];
            }
        }
        return $tree;

    }

    protected function buildPidMenu($pid, $list, $level = 0)
    {
        $newList = [];
        foreach ($list as $vo) {
            if ($vo['pid'] == $pid) {
                $level++;
                foreach ($newList as $v) {
                    if ($vo['pid'] == $v['pid'] && isset($v['level'])) {
                        $level = $v['level'];
                        break;
                    }
                }
                $vo['level'] = $level;
                if ($level > 1) {
                    $repeatString = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
                    $markString   = str_repeat("{$repeatString}├{$repeatString}", $level - 1);
                    $vo['title']  = $markString . $vo['title'];
                }
                $newList[] = $vo;
                $childList = $this->buildPidMenu($vo['id'], $list, $level);
                !empty($childList) && $newList = array_merge($newList, $childList);
            }

        }
        return $newList;
    }


}