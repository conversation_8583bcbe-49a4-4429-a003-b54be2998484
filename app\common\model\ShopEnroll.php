<?php

namespace app\common\model;

use app\shop\traits\ModelTrait;

class ShopEnroll extends TimeModel
{
	
	use ModelTrait;

    protected $type = [
        'enroll_form' => 'array',
    ];

    protected $name = "shop_enroll";

    protected $deleteTime = "delete_time";

    public function getEnrollStatusList()
    {
        return ['0' => '关闭', '1' => '打开',];
    }

    public static function getEnrollInfo($id,$shop_id)
    {
        $info = self::where('shop_id',$shop_id)->where('enroll_status',1)->find($id);
        if (!empty($info)) {
            $enrollArr = $info->enroll_form;
            foreach ($enrollArr as $key => $item) {
                $type = intval($item['type']);
                if ($type > 1 && $type < 5) {
                    // 分割字符串
                    $fieldArr = explode(',',$item['place']);
                    if (empty($fieldArr)) {
                        continue;
                    }
                    if ($type === 3) {
                        foreach($fieldArr as $index => $value) {
                            $fieldArr[$index] = [
                                'name'=>$value,
                                'checked'=>false,
                                'disabled'=> false
                            ];
                        }
                    }
                    if ($type === 4) {
                        foreach($fieldArr as $index => $value) {
                            $fieldArr[$index] = [
                                'text'=>$value
                            ];
                        }
                    }
                    $enrollArr[$key]['place'] = $fieldArr;
                }
                if ($type === 4 || $type === 5) {
                    $enrollArr[$key]['show'] = false;
                }
            }
            $info->enroll_form_str = $enrollArr;
            $info->hidden(['shop_id','enroll_form','update_time','delete_time']);
        }
        return $info;
    }

}