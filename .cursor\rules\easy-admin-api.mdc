---
description: 
globs: 
alwaysApply: false
---
# Easy-Admin 框架功能与API

[easy-admin.js](mdc:public/static/plugs/easy-admin/easy-admin.js) 是项目的核心前端框架，基于jQuery和LayUI，提供了丰富的UI组件和管理功能。

## 核心对象
- `admin`: 主对象，包含所有功能模块
  - `config`: 全局配置
  - `request`: AJAX请求处理
  - `table`: 表格组件
  - `msg`: 消息提示
  - `common`: 通用工具

## 表格功能
表格是后台管理系统最核心的组件，由 `admin.table` 提供：

```javascript
admin.table.render({
    elem: '#currentTable',
    url: '{:url("index")}',
    cols: [[
        {type: "checkbox"},
        {field: 'id', title: 'ID'},
        {field: 'title', title: '标题', minWidth: 100},
        {field: 'status', title: '状态', templet: admin.table.switch},
        {field: 'image', title: '图片', templet: admin.table.image},
        {field: 'create_time', title: '创建时间', search: 'range'},
        {width: 250, title: '操作', templet: admin.table.tool}
    ]],
    search: true,
    toolbar: ['refresh','add','delete','export']
});
```

## 表单处理
表单提交和验证功能：

```javascript
admin.api.form(url, data, function(res) {
    // 成功回调
}, function(res) {
    // 失败回调
});
```

## 文件上传
文件上传组件配置：

```html
<div class="layui-form-item">
    <label class="layui-form-label">图片</label>
    <div class="layui-input-block">
        <input name="image" data-upload="image" data-upload-number="one" class="layui-input">
    </div>
</div>
```

## 弹窗和消息
弹窗和消息提示：

```javascript
admin.msg.success('操作成功');
admin.msg.error('操作失败');
admin.msg.confirm('确认操作？', function() {
    // 确认回调
});

// 打开弹窗
admin.open('编辑', '{:url("edit")}?id=1', '800px', '600px');
```

## 权限检查
权限检查功能：

```javascript
if (admin.checkAuth('add', elem)) {
    // 有添加权限
}
```

## 监听事件
监听表单和表格事件：

```javascript
admin.listen(function(data) {
    // 表单预处理
    return data;
});
```

## AJAX请求
AJAX请求封装：

```javascript
admin.request.post({
    url: 'url',
    data: {id: 1},
    prefix: true,
}, function(res) {
    // 成功回调
});
```

