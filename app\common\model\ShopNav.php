<?php

namespace app\common\model;

use app\shop\traits\ModelTrait;

class ShopNav extends TimeModel
{

    protected $name = "shop_nav";

    protected $deleteTime = "delete_time";

    public function getListTypeList()
    {
        return ['0' => '原始比例', '1' => '横版', '2' => '竖版', '3' => '正方形'];
    }

    public function getPidNavList($shopId)
    {
        $pidMenuList = $this->field('id,p_id,title_nav')
            ->where([
                ['shop_id', '=', $shopId],
                ['p_id', '=', 0],
            ])
            ->order('sort desc')
            ->select()
            ->toArray();
        return array_merge([[
            'id' => 0,
            'pid' => 0,
            'title_nav' => '顶级菜单',
        ]], $pidMenuList);
    }

    public function getNavList($shopId)
    {
        $list = $this->field('id,p_id,title_nav')
            ->where([
                ['shop_id', '=', $shopId],
            ])
            ->order('sort desc')
            ->select()
            ->toArray();
        return $this->buildPidMenu(0, $list);
    }

    public function getMainNavList($shopId)
    {
        return $this->field('id,title_nav,title_nav_en,list_type,cat_image,content_id')
            ->where([
                ['shop_id', '=', $shopId],
                ['is_main', '=', 1],
                ['is_mini', '=', 1],
            ])
            ->order(['sort'=>'desc','create_time'=>'desc'])
            ->select();
    }

    // 获取分类下子分类
    public function getChildNavList($navId,$shopId)
    {
        return $this->field('id,title_nav,title_nav_en,list_type,cat_image,content_id')
            ->where([
                ['shop_id', '=', $shopId],
                ['p_id','=',$navId],
                ['is_main', '=', 0],
                ['is_mini', '=', 1],
            ])
            ->order(['sort'=>'desc','create_time'=>'desc'])
            ->select();
    }

    public function getChild($navId,$shopId)
    {
        return $this->field('id')
            ->where([
                ['shop_id', '=', $shopId],
                ['p_id','=',$navId],
                ['is_main', '=', 0],
                ['is_mini', '=', 1],
            ])
            ->order(['sort'=>'desc','create_time'=>'desc'])
            ->find();
    }

    protected function buildPidMenu($pid, $list, $level = 0)
    {
        $newList = [];
        foreach ($list as $vo) {
            if ($vo['p_id'] == $pid) {
                $level++;
                foreach ($newList as $v) {
                    if ($vo['p_id'] == $v['p_id'] && isset($v['level'])) {
                        $level = $v['level'];
                        break;
                    }
                }
                $vo['level'] = $level;
                if ($level > 1) {
                    $repeatString = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
                    $markString = str_repeat("{$repeatString}├{$repeatString}", $level - 1);
                    $vo['title_nav'] = $markString . $vo['title_nav'];
                }
                $newList[] = $vo;
                $childList = $this->buildPidMenu($vo['id'], $list, $level);
                !empty($childList) && $newList = array_merge($newList, $childList);
            }

        }
        return $newList;
    }


}