<?php



namespace app\admin\controller\system;


use app\admin\model\SystemAuth;
use app\admin\model\SystemAuthNode;
use app\admin\service\TriggerService;
use app\common\controller\AdminController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use think\App;

/**
 * @ControllerAnnotation(title="角色权限管理")
 * Class Auth
 * @package app\admin\controller\system
 */
class Auth extends AdminController
{

    protected $sort = [
        'sort' => 'desc',
        'id'   => 'desc',
    ];

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new SystemAuth();
    }
	
	/**
	 * @NodeAnotation(title="列表")
	 */
	public function index()
	{
		if ($this->request->isAjax()) {
			if (input('selectFields')) {
				return $this->selectList();
			}
			list($page, $limit, $where) = $this->buildTableParames();
			$count = $this->model->where($where)
			                     ->count();
			$list  = $this->model->where($where)
			                     ->page($page, $limit)
			                     ->order($this->sort)
			                     ->select();
			$data  = [
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $list,
			];
			return json($data);
		}
		return $this->fetch();
	}

    /**
     * @NodeAnotation(title="授权")
     */
    public function authorize($id)
    {
        $row = $this->model->find($id);
        empty($row) && $this->error('数据不存在');
        if ($this->request->isAjax()) {
            $list = $this->model->getAllNodeListByAdminId($id);
            $this->success('获取成功', $list);
        }
        $this->assign('row', $row);
        return $this->fetch();
    }

    /**
     * @NodeAnotation(title="授权保存")
     */
    public function saveAuthorize()
    {
        $id = $this->request->post('id');
        $node = $this->request->post('node', "[]");
        $node = json_decode($node, true);
        $row = $this->model->find($id);
        empty($row) && $this->error('数据不存在');
        try {
            $authNode = new SystemAuthNode();
            $authNode->where('auth_id', $id)->delete();
            if (!empty($node)) {
                $saveAll = [];
                foreach ($node as $vo) {
                    $saveAll[] = [
                        'auth_id' => $id,
                        'node_id' => $vo,
                    ];
                }
                $authNode->saveAll($saveAll);
            }
            TriggerService::updateMenu();
        } catch (\Exception $e) {
            $this->error('保存失败');
        }
        $this->success('保存成功');
    }
	
	/**
	 * @NodeAnotation(title="添加")
	 */
	public function add()
	{
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $this->model->saveFromCreateId($post);
			} catch (\Exception $e) {
				$this->error('保存失败:' . $e->getMessage());
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('scopeList', SystemAuth::getScopeList());
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="编辑")
	 * @param $id
	 * @return
	 */
	public function edit($id)
	{
		$row = $this->model->find($id);
		empty($row) && $this->error('数据不存在');
		if ($this->request->isAjax()) {
			$post = $this->request->post();
			$rule = [];
			$this->validate($post, $rule);
			try {
				$save = $row->saveFromCreateId($post);
			} catch (\Exception $e) {
				$this->error('保存失败');
			}
			$save
				? $this->success('保存成功')
				: $this->error('保存失败');
		}
		$this->assign('row', $row);
		$this->assign('scopeList', SystemAuth::getScopeList());
		return $this->fetch();
	}
	
	/**
	 * @NodeAnotation(title="删除")
	 */
	public function delete($id)
	{
		$row = $this->model->whereIn('id', $id)->select();
		$row->isEmpty() && $this->error('数据不存在');
		try {
			$save = $row->delete();
		} catch (\Exception $e) {
			$this->error('删除失败');
		}
		$save ? $this->success('删除成功') : $this->error('删除失败');
	}

}