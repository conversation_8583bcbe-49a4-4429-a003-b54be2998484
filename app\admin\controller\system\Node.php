<?php

namespace app\admin\controller\system;

use app\admin\model\SystemNode;
use app\admin\service\AdminNodeService;
use app\admin\service\TriggerService;
use app\common\controller\AdminController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use think\App;

/**
 * @ControllerAnnotation(title="系统节点管理")
 * Class Node
 * @package app\admin\controller\system
 */
class Node extends AdminController
{

    protected $nodeService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new SystemNode();
        $this->nodeService = new AdminNodeService();
    }

    /**
     * @NodeAnotation(title="列表")
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            if (input('selectFields')) {
                return $this->selectList();
            }
            $count = $this->model
                ->count();
            $list  = $this->model
                ->getNodeTreeList();
            $data  = [
                'code'  => 0,
                'msg'   => '',
                'count' => $count,
                'data'  => $list,
            ];
            return json($data);
        }
        return $this->fetch();
    }

    /**
     * @NodeAnotation(title="系统节点更新")
     */
    public function refreshNode($force = 0)
    {
        $nodeList = $this->nodeService->getNodelist();
        empty($nodeList) && $this->error('暂无需要更新的系统节点');
        $model = new SystemNode();
        try {
            if ($force == 1) {
                $updateNodeList = $model->whereIn('node', array_column($nodeList, 'node'))->select();
                $formatNodeList = array_format_key($nodeList, 'node');
                foreach ($updateNodeList as $vo) {
                    isset($formatNodeList[$vo['node']]) && $model->where('id', $vo['id'])->update([
                        'title'   => $formatNodeList[$vo['node']]['title'],
                        'is_auth' => $formatNodeList[$vo['node']]['is_auth'],
                    ]);
                }
            }
            $existNodeList = $model->field('node,title,type,is_auth')->select();
            foreach ($nodeList as $key => $vo) {
                foreach ($existNodeList as $v) {
                    if ($vo['node'] == $v->node) {
                        unset($nodeList[$key]);
                        break;
                    }
                }
            }
            $model->saveAll($nodeList);
            TriggerService::updateNode();
        }
        catch (\Exception $e) {
            $this->error('节点更新失败');
        }
        $this->success('节点更新成功');
    }

    /**
     * @NodeAnotation(title="清除失效节点")
     */
    public function clearNode()
    {
        $nodeList = $this->nodeService->getNodelist();
        $model    = new SystemNode();
        try {
            $existNodeList  = $model->field('id,node,title,type,is_auth')->select()->toArray();
            $formatNodeList = array_format_key($nodeList, 'node');
            foreach ($existNodeList as $vo) {
                !isset($formatNodeList[$vo['node']]) && $model->where('id', $vo['id'])->delete();
            }
            TriggerService::updateNode();
        }
        catch (\Exception $e) {
            $this->error('节点更新失败');
        }
        $this->success('节点更新成功');
    }
	
	/**
	 * @NodeAnotation(title="属性修改")
	 */
	public function modify()
	{
		$post = $this->request->post();
		$rule = [
			'id|ID'    => 'require',
			'field|字段' => 'require',
			'value|值'  => 'require',
		];
		$this->validate($post, $rule);
		$row = $this->model->find($post['id']);
		if (!$row) {
			$this->error('数据不存在');
		}
		if (!in_array($post['field'], $this->allowModifyFields)) {
			$this->error('该字段不允许修改：' . $post['field']);
		}
		try {
			$row->save([
				$post['field'] => $post['value'],
			]);
		} catch (\Exception $e) {
			$this->error($e->getMessage());
		}
		$this->success('保存成功');
	}
}