var BASE_URL = document.scripts[document.scripts.length - 1].src.substring(0, document.scripts[document.scripts.length - 1].src.lastIndexOf("/") + 1);
window.BASE_URL = BASE_URL;
console.log("BASE_URL", BASE_URL);
require.config({
    urlArgs: "v=" + CONFIG.VERSION,
    baseUrl: BASE_URL,
    paths: {
        "jquery": ["plugs/jquery-3.4.1/jquery-3.4.1.min"],
        "jquery.particleground": ["plugs/jq-module/jquery.particleground.min"],
        "jquery.searchableSelect": ["plugs/jq-module/searchable_select/searchable_select"],
        "sortable": ["plugs/sortable/Sortable.min"],
        "echarts": ["plugs/echarts/echarts.min"],
        "echarts-theme": ["plugs/echarts/echarts-theme"],
        "easy-admin": ["plugs/easy-admin/easy-admin"],
        /*"layuiall": ["plugs/layui-v2.5.6/layui.all"],
        "layui": ["plugs/layui-v2.5.6/layui"],*/
        "layui": ["plugs/layui-v2.8.x/layui"],
        "miniAdmin": ["plugs/lay-module/layuimini/miniAdmin"],
        "miniMenu": ["plugs/lay-module/layuimini/miniMenu"],
        "miniTab": ["plugs/lay-module/layuimini/miniTab"],
        "miniTheme": ["plugs/lay-module/layuimini/miniTheme"],
        "miniTongji": ["plugs/lay-module/layuimini/miniTongji"],
        "treetable": ["plugs/lay-module/treetable-lay/treetable"],
        "tableSelect": ["plugs/lay-module/tableSelect/tableSelect"],
        "xm-select": ["plugs/lay-module/xm-select/xm-select"],
        "iconPickerFa": ["plugs/lay-module/iconPicker/iconPickerFa"],
        "autocomplete": ["plugs/lay-module/autocomplete/autocomplete"],
        // "locationX": ["plugs/lay-module/location/locationX"],
        "location": ["plugs/lay-module/location/location"],
        "layarea": ["plugs/lay-module/layarea/layarea"],
        "vue": ["plugs/vue-2.6.10/vue.min"],
        "ckeditor": ["plugs/ckeditor4/ckeditor"],
        "tinymce": ["plugs/tinymce/tinymce"],
        "clipboard": ["plugs/clipboard/clipboard.min"],
        "qrcode": ["plugs/lay-module/qrcode/qrcode"],
    },
    shim: {
        'jquery.searchableSelect': {
            deps: ['jquery'],
            exports: "searchableSelect"
        },
        'jquery.particleground': {
            deps: ['jquery'],
            // exports: "particleground"
        },
        'qrcode': {
            deps: ['jquery'],
        },
    },
});

// 路径配置信息
var PATH_CONFIG = {
    iconLess: BASE_URL + "plugs/font-awesome-4.7.0/less/variables.less",
};
layui.config({
    base: BASE_URL + 'plugs/lay-module/' //设定扩展的 layui 模块的所在目录，一般用于外部模块扩展
});
window.PATH_CONFIG = PATH_CONFIG;

// 初始化控制器对应的JS自动加载
if ("undefined" != typeof CONFIG.AUTOLOAD_JS && CONFIG.AUTOLOAD_JS) {
    require([BASE_URL + CONFIG.CONTROLLER_JS_PATH], function (Controller) {

        if (Controller[CONFIG.ACTION] && typeof Controller[CONFIG.ACTION] === 'function') {
            Controller[CONFIG.ACTION]()
        }
        /*if (eval('Controller.' + CONFIG.ACTION)) {
            eval('Controller.' + CONFIG.ACTION + '()');
        }*/
    });
}