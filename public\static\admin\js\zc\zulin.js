define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'zc.zulin/index',
        add_url: 'zc.zulin/add',
        edit_url: 'zc.zulin/edit',
        delete_url: 'zc.zulin/delete',
        export_url: 'zc.zulin/export',
        modify_url: 'zc.zulin/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh', 'add'],
                cols: [[
                    {type: 'checkbox'},
                    {type: 'numbers', title: '序号'},
                    {field: 'no', title: '编号'},
                    {field: 'title', title: '租赁单位名称'},
                    {field: 'name', title: '负责人姓名'},
                    {field: 'mobile', title: '负责人电话'},
                    // {field: 'id_card_images', title: '负责人身份证图片'},
                    {field: 'fa_name', title: '法人姓名'},
                    {field: 'fa_mobile', title: '法人电话'},
                    // {field: 'faren_images', title: '法人身份证图片'},
                    {field: 'desc', title: '备注',search: false},
                    {field: 'score', title: '评分',search: false,templet: function (d) {
                         return d.score < 0 ? '未评分' : d.score;
                        }},
                    {field: 'creator_name', title: '创建人',search: false},
                    {field: 'create_time', title: '创建时间',search: 'date_range'},
                    {width: 160, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});