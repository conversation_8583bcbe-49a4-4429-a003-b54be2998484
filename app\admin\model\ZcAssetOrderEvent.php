<?php

namespace app\admin\model;

use app\admin\traits\ModelTrait;
use app\common\model\TimeModel;

class ZcAssetOrderEvent extends TimeModel
{
	use ModelTrait;

    protected $name = "zc_asset_order_event";

    protected $deleteTime = "delete_time";

    
    public function asset()
    {
        return $this->belongsTo('\app\admin\model\ZcAsset', 'asset_id', 'id');
    }
	
	public function setEventStartDateAttr($value)
	{
		return $value
			? strtotime($value)
			: '';
	}
	
	public function getEventStartDateAttr($value)
	{
		return $value
			? date("Y-m-d", $value)
			: '';
	}
	
	public function setEventEndDateAttr($value)
	{
		return $value
			? strtotime($value)
			: '';
	}
	
	public function getEventEndDateAttr($value)
	{
		return $value
			? date("Y-m-d", $value)
			: '';
	}

}