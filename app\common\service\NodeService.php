<?php


namespace app\common\service;


use Doctrine\Common\Annotations\AnnotationException;
use EasyAdmin\auth\Node;
use ReflectionException;

abstract class NodeService
{
	
	protected $moduleName;
	
	/**
	 * 获取节点服务
	 * @return array
	 * @throws AnnotationException
	 * @throws ReflectionException
	 */
	public function getNodelist()
	{
		$basePath      = base_path() . $this->moduleName . DIRECTORY_SEPARATOR . 'controller';
		$baseNamespace = 'app' . '\\' . $this->moduleName . '\\' . 'controller\\';
		
		return (new Node($basePath, $baseNamespace))->getNodelist();
	}
}