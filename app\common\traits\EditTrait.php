<?php

namespace app\common\traits;

use EasyAdmin\annotation\NodeAnotation;

/**
 * EditTrait
 * @package app\admin\traits
 */
trait EditTrait
{
    /**
     * @NodeAnotation(title="编辑")
     * @param $id
     * @return
     */
    public function edit($id)
    {
        $row = $this->model->find($id);
        empty($row) && $this->error('数据不存在');
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $rule = [];
            $this->validate($post, $rule);
            try {
                $save = $row->save($post);
            } catch (\Exception $e) {
                $this->error('保存失败');
            }
            $save ? $this->success('保存成功') : $this->error('保存失败');
        }
        $this->assign('row', $row);
        return $this->fetch();
    }

}
