---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

## 当前项目使用的是thinkphp6
## 前端使用html,jquery,layui
## 目录结构
- `app/`: 应用核心代码目录
  - `index/`: 前台模块
  - `admin/`: 后台管理模块
  - `common/`: 公共模块
  - `command/`: 命令行工具
- `config/`: 配置文件目录
- `route/`: 路由定义目录
- `public/`: 公共资源和入口文件
- `runtime/`: 运行时目录，存放日志和缓存
- `vendor/`: Composer依赖库

## 核心文件
- [composer.json](mdc:composer.json): 项目依赖管理
- [think](mdc:think): 命令行入口
- [app/BaseController.php](mdc:app/BaseController.php): 控制器基类
- [app/common.php](mdc:app/common.php): 公共函数
- [config/app.php](mdc:config/app.php): 应用配置
- [config/database.php](mdc:config/database.php): 数据库配置
- [route/app.php](mdc:route/app.php): 应用路由

