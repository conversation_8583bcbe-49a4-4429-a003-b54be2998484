<?php

namespace app\common\traits;

trait NodeTreeTrait
{
    public function getNodeTreeList()
    {
        $list = $this->select()->toArray();
        return $this->buildNodeTree($list);
    }

    protected function buildNodeTree($list)
    {
        $newList = [];
        $repeatString = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
        foreach ($list as $vo) {
            if ($vo['type'] == 1) {
                $newList[] = $vo;
                foreach ($list as $v) {
                    $info = strpos($v['node'], $vo['node'] . '/');
                    if ($v['type'] == 2 && $info === 0) {
                        $v['node'] = "{$repeatString}├{$repeatString}" . $v['node'];
                        $newList[] = $v;
                    }
                }
            }
        }
        return $newList;
    }
}