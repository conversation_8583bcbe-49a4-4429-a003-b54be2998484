---
description: 
globs: 
alwaysApply: false
---
# 前端模块加载与配置

项目使用RequireJS进行模块化管理，主要配置文件是 [public/static/config-admin.js](mdc:public/static/config-admin.js)。

## 模块配置

```javascript
require.config({
    baseUrl: BASE_URL,
    paths: {
        "jquery": ["plugs/jquery-3.4.1/jquery-3.4.1.min"],
        "layui": ["plugs/layui-v2.8.x/layui"],
        "easy-admin": ["plugs/easy-admin/easy-admin"],
        // 更多模块配置...
    },
    // 模块依赖关系配置...
});
```

## 基本用法

1. 定义页面控制器：

```javascript
// 控制器JS文件
define(["jquery", "easy-admin"], function ($, admin) {
    var Controller = {
        index: function () {
            // 列表页面逻辑
            admin.table.render({
                // 表格配置...
            });
        },
        add: function () {
            // 添加页面逻辑
            admin.listen();
        },
        edit: function () {
            // 编辑页面逻辑
            admin.listen();
        }
    };
    return Controller;
});
```

2. 自动加载控制器：

当页面加载时，系统会自动根据当前控制器和方法名，加载对应的JS文件和方法。例如：
- 控制器：admin/User
- 方法：index
- 会自动加载：static/js/admin/user.js中的index方法

## 常用模块

1. **jquery**: 基础DOM操作
2. **layui**: UI组件库
   - form: 表单组件
   - table: 表格组件
   - layer: 弹层组件
   - upload: 上传组件
   - laydate: 日期组件
3. **easy-admin**: 后台管理框架
4. **echarts**: 图表库
5. **tinymce/ckeditor**: 富文本编辑器
6. **vue**: 前端MVVM框架

## LayUI模块扩展

项目在LayUI基础上扩展了多个模块：
- miniAdmin: 后台布局模块
- miniMenu: 菜单模块
- miniTab: 选项卡模块
- tableSelect: 表格选择器
- xm-select: 下拉多选
- treetable: 树形表格

