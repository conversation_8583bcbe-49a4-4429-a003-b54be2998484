<?php

namespace app\common\model;

use app\shop\traits\ModelTrait;

class ShopContent extends TimeModel
{
	
	use ModelTrait;
	

    protected $name = "shop_content";

    protected $deleteTime = "delete_time";

    public function getIsMobileShowList()
    {
        return ['0' => '否', '1' => '是',];
    }

    public function getOnlyShareList()
    {
        return ['0' => '否', '1' => '是',];
    }

    public function getContentTitleAttr($value)
    {
        return htmlspecialchars_decode($value);
    }

    public function getIsPswAttr($value,$data)
    {
        return !empty($data['view_psw']);
    }

    public function nav()
    {
        return $this->belongsTo(ShopNav::class, 'nav_id');
    }

    public function favorites()
    {
        return $this->hasMany(UserFavorite::class, 'content_id');
    }

    public static function getContentList($shopId,$flagId)
    {
        return self::where('shop_id', $shopId)->where('is_pc_show',1)->whereFindInSet('flag_list',$flagId)->select()->hidden(['nav_id','only_share','only_share_time','view_psw','create_time','update_time','delete_time']);
    }


}