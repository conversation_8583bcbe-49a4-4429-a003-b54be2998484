<?php

namespace app\common\traits;

use EasyAdmin\annotation\NodeAnotation;

/**
 * DeleteTrait
 * @package app\admin\traits
 */
trait DeleteTrait
{
    /**
     * @NodeAnotation(title="删除")
     */
    public function delete($id)
    {
        $row = $this->model->whereIn('id', $id)->select();
        $row->isEmpty() && $this->error('数据不存在');
        try {
            $save = $row->delete();
        } catch (\Exception $e) {
            $this->error('删除失败');
        }
        $save ? $this->success('删除成功') : $this->error('删除失败');
    }

}
